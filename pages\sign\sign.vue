<template>
	<view class="page-box">
		<!-- 固定背景图片 -->
		<image
			src="../../static/image/signBack1.png"
			mode="widthFix"
			class="background-image"
		></image>

		<u-navbar bgColor="#ffffff00" height="44" :placeholder='true' :auto-back="false" left-icon="">

		</u-navbar>
		<view class="dis-ali jc_cen">
			<view style="flex-direction: column;" class="dis-ali   ">
				<view class="content" style="align-self: flex-start;margin:54upx 0 50upx 30upx;font-size: 48rpx;color: #fff;z-index: 999;">
					欢迎{{signWay?'注册':'登录'}}
					 <view v-if="!signWay" class="content" style="font-size: 32rpx;margin-top: 20upx;color: #fff;">新用户请先进行注册
				</view> 
				</view>

				<view class="topbar_view com-fontsize-32 dis-ali "
					style=" padding: 50upx 90upx;align-items: start;position: relative;margin-bottom: -5upx;">
					<view class="dis-ali flex-column">
						<view>
							手机号{{signWay?'注册':'登录'}}
						</view>
						<view class="line" style=""></view>
					</view>
					
				</view>


				<view style="width: 680upx;min-height: 750upx;background-color: #fff;padding-bottom: 60upx;z-index: 999;"
					class="dis-ali jc_cen flex-column ">
					<view>
						<view class="mb10 com-color">
							手机号
						</view>
						<u-input   :adjustPosition='false'  type='number' v-model="form.mobile" placeholder="输入您的手机号" height='50'
							style="width: 570upx;background-color: #F5F8FF;height: 100upx;border: 0;"
							prefixIconStyle="font-size: 22px;color: #909399" fontSize='14px'>
							<!-- <template slot="prefix">
								<text class="com-fontsize-28 mr10">手机号</text>
							</template> -->
							<template slot="suffix">
								<u-icon name="info-circle" size="17" :bold="true" style="margin-right: 10upx ;"
									@click="show=true"></u-icon>
							</template>
						</u-input>
					</view>
					<view v-if="loginType === 'code'">
						<view class="mb10 com-color mt10">
							验证码
						</view>
						<view>
							<u-input   :adjustPosition='false'   type='number' v-model="form.code"  placeholder="输入验证码" height='50'
								style="width: 570upx;background-color: #F5F8FF;height: 100upx;border: 0;"
								prefixIconStyle="font-size: 22px;color: #909399" fontSize='14px'>

								<template slot="suffix">
									<view class="dis-ali">
										<!-- <u-icon name="info-circle" size="17" :bold="true" style="margin-right: 50upx ;"
											@click="show=true"></u-icon> -->
										<u-code ref="uCode" @change="codeChange" seconds="60"
											changeText="重新获取X秒"></u-code>
										<u-text @tap="getCode" :text="tips" color="#FFA528"></u-text>
									</view>

								</template>
							</u-input>
						</view>
					</view>
					<view v-if="loginType === 'password'">
						<view class="mb10 com-color mt10">
							密码
						</view>
						<view>
							<u-input   :adjustPosition='false'   type='password' v-model="form.password"  placeholder="输入密码" height='50'
								style="width: 570upx;background-color: #F5F8FF;height: 100upx;border: 0;"
								prefixIconStyle="font-size: 22px;color: #909399" fontSize='14px'>
							</u-input>
						</view>
					</view>
					<view v-if="signWay">
						<view class="mb10 com-color mt10">
							邀请码
						</view>
						<u-input   :adjustPosition='false'  v-model="form.inviteCode" placeholder="输入邀请码(非必填)" type="text" height='50'
							style="width: 570upx;background-color: #F5F8FF;height: 100upx;border: 0;" fontSize='14px'>
						</u-input>
					</view>
					<view style="font-size: 28upx;justify-content: space-between;width: 570upx;" class="dis-ali mt10">
						<view style="color: #FFA528;" @click="signWay=!signWay">{{signWay?'已有账号，去登录':'没有账号，去注册'}}</view>
						<view style="color: #999999;" @click="goUrl(2)">暂不登录</view>

					</view>
					<view @click="sign" class="signBtn" style="">
						{{signWay?'注册':'登录'}}
					</view>
					<view class="dis-ali " style="justify-content: flex-start;width: 570upx;margin-top: 30upx;">
						<u-checkbox-group activeColor='#FFA528' iconPlacement='left' v-model="btn" placement="column"
							@change="ChangeIntegral">
							<u-checkbox name="1" shape='circle' size="14">
							</u-checkbox>
						</u-checkbox-group>
						<view style="font-size: 26upx;color: #939393;">
							我已阅读并同意<span style="color:  #FFA528;" @click="goUrl(4)">用户服务协议，</span><span
								@click="goUrl(5)" style="color:  #FFA528;">隐私政策</span>
						</view>
						
					</view>
					<view class="login-type-switch" @click="toggleLoginType">
						{{ loginType === 'code' ? '密码登录' : '验证码登录' }}
					</view>
				</view>

				<u-popup :show="show" mode="center" round='5' :safeAreaInsetBottom='false'>
					<view
						style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
						<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
						<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
							同意/允许生意港向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系客服处理
							</view>

						<view @click="show = false" class="showBtn" style="">
							我知道了
						</view>
					</view>
				</u-popup>

			</view>
		</view>
		<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
			:loading="loading"></u-loading-page>

	</view>
</template>

<script>
	var that	
	import sygajax from '../../sygajax.js'
	// 导入自定义MD5加密库
	import w_md5 from "../../utils/w_md5.js"

	export default {
		data() {
			return {
				show: false,
				btn: [],
				tips: '',
				value: '',
				form: {
					mobile: '',
					inviteCode:'',
					password: '',
					code: '',
				},
				signWay: false,
				isActive: false,
				loading: false,
				loginType: 'code' // 'code'或'password'，用于切换登录方式
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		onLoad() {
			that = this;
			that.loading = false
		},
		onShow() {
			that = this;
			console.log(getApp().globalData.login)
			if(getApp().globalData.login){
				uni.switchTab({
					url:'/pages/index/index',
					success() {
							that.loading = false
					}
				})
			}
		},
		methods: {
			// 切换登录方式
			toggleLoginType() {
				this.loginType = this.loginType === 'code' ? 'password' : 'code';
			},
			touchend() {
				setTimeout(() => this.isActive = false, 2000)
			},
			// MD5加密密码 - 使用32位小写，并添加固定字符串dfshgs
			encryptPassword(password) {
				try {
					// 拼接固定字符串dfshgs
					const passwordWithSuffix = password + 'dfshgs';
					return w_md5.hex_md5_32(passwordWithSuffix);
				} catch (e) {
					console.error('MD5加密失败:', e);
					return password; // 如果加密失败，返回原始密码
				}
			},
			// 处理登录/注册成功后的操作
			handleLoginSuccess(res) {
				if (res.code == 1000) {
					getApp().globalData.login = true
					
					// 存储token
					if (res.data && res.data.token) {
						uni.setStorageSync('sygtoken', res.data.token);
					}
					
					// 存储默认公司信息(如果有)
					if (res.data && res.data.defaultCompany) {
						uni.setStorageSync('defaultCompany', res.data.defaultCompany);
					} else {
						// 如果defaultCompany为null，可能需要清除之前的缓存
						uni.removeStorageSync('defaultCompany');
					}
					
					// 显示提示并跳转
					uni.showToast({
						title: this.signWay ? '注册成功' : '登录成功',
						icon: 'none',
						duration: 1000,
						success: () => {
							// 检查是否首次打开应用
							const isFirstOpen = !uni.getStorageSync('notFirstOpen');
							if (!isFirstOpen) {
								// 使用switchTab跳转到tabBar页面
								uni.switchTab({
									url: '/pages/investment/index'
								})
							} else {
								uni.navigateTo({
									url: '/pages/guide/guide'
								})
							}
						}
					})
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			},
			sign() {
				if (!uni.$u.test.mobile(that.form.mobile)) return uni.$u.toast('请填写正确的手机号');
				
				// 注册
				if (this.signWay) {   
					if (!uni.$u.test.code(that.form.code, 4)) return uni.$u.toast('请填写正确的验证码');
					if (this.btn.length != 0) {
						const registerData = {
							phone: that.form.mobile,
							msgCode: that.form.code,
							password: this.form.password ? this.encryptPassword(this.form.password) : this.encryptPassword('123456'), // 加密密码
							companyInviteCode: that.form.inviteCode || ''
						};
						
						sygajax.ajax({
							url: sygajax.api.creatememberV1,
							method: 'POST',
							data: registerData,
							success: (res) => {
								console.log('注册返回:', res);
								this.handleLoginSuccess(res);
							}
						})
					} else {
						uni.$u.toast('请阅读并同意用户协议和隐私政策');
					}
				} else {
					// 登录
					if (this.btn.length != 0) {
						let loginData = {
							phone: this.form.mobile
						};
						
						// 根据登录类型设置参数
						if (this.loginType === 'code') {
							if (!uni.$u.test.code(that.form.code, 4)) return uni.$u.toast('请填写正确的验证码');
							loginData.securitycode = this.form.code;
						} else {
							if (!this.form.password) return uni.$u.toast('请填写密码');
							// 对密码进行MD5加密 - 32位小写
							loginData.password = this.encryptPassword(this.form.password);
						}
						
						sygajax.ajax({
							url: sygajax.api.phoneLoginV2,
							method: 'POST',
							data: loginData,
							success: (res) => {
								console.log('登录返回:', res);
								this.handleLoginSuccess(res);
							}
						})
					} else {
						uni.$u.toast('请阅读并同意用户协议和隐私政策');
					}
				}
			},
			ChangeIntegral() {
				// 勾选协议后的处理
			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					if (!uni.$u.test.mobile(that.form.mobile)) return uni.$u.toast('请填写正确的手机号');
					
					uni.showLoading({
						title: '正在获取验证码'
					})
					
					// 使用GET方法调用getMsgCode接口
					uni.request({
						url: sygajax.baseUrl + sygajax.api.getMsgCode,
						method: 'GET',
						data: {
							phone: that.form.mobile
						},
						header: {
							'Content-Type': 'application/json',
							'Authorization': uni.getStorageSync('sygtoken') || '',
							'identification': 'saas_syg',
							'os': sygajax.getDeviceType()
						},
						success: (res) => {
							uni.hideLoading();
							console.log(res)
							if (res.data.code == 1000) {
								uni.$u.toast('验证码已发送');
								that.$refs.uCode.start();
							} else {
								uni.showToast({
									title: res.data.msg,
									icon: 'none'
								})
							}
						},
						fail: (err) => {
							uni.hideLoading();
							uni.showToast({
								title: '获取验证码失败',
								icon: 'none'
							})
						}
					})
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			goUrl(type) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/sign/regiter'
					})
				}
				if (type == 2) {
					uni.switchTab({
						url: '/pages/investment/sygindex'
					})
				}
				if (type == 4) {
						uni.navigateTo({
							url: `/pages/yinsi/policy?type=1`
						})
				}
				if (type == 5) {
					uni.navigateTo({
						url: `/pages/yinsi/policy?type=2`
					})
				}
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #fff;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.page-box {
		position: relative;
		min-height: 100vh;
		// overflow: hidden;
	}

	.background-image {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: 1;
		object-fit: cover;
	}

	.topbar_view {
		z-index: 999;
		background-image: url(../../static/topbar.png);
		background-size: 100% 100%;
		width: 680upx;
		// height: 157upx;

		.line {
			margin-top: 10upx;
			width: 48rpx;
			height: 7rpx;
			background: linear-gradient(129deg, rgba(255, 165, 40, 0.38) 0%, rgba(255, 165, 40, 0.8) 99%);
			border-radius: 20rpx 20rpx 20rpx 20rpx;
		}
	}

	.showBtn {
		font-size: 34upx;
		color: #fff;
		background-color: #f4ad32;
		width: 500upx;
		height: 100upx;
		border-radius: 10upx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30upx;
	}

	.signBtn {
		font-size: 34upx;
		color: #fff;
		background-color: #FFA528;
		width: 570upx;
		height: 112upx;
		border-radius: 20upx;
		display: flex;
		align-items: center;
		box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.2);
		justify-content: center;
		margin-top: 30upx;
	}
	
	.login-type-switch {
		font-size: 28upx;
		color: #FFA528;
		margin-top: 30upx;
		padding: 10upx 20upx;
		border-radius: 30upx;
		background-color: #F5F8FF;
		display: inline-block;
		transition: all 0.3s;
		
		&:active {
			opacity: 0.8;
		}
	}
</style>