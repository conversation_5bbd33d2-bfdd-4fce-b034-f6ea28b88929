var ajax = {
	
	// baseurl: "https://zhaolian.121.huanxingweb.com/api/alc/v1/",
	// #ifdef H5
	  // baseUrl: "/api/admin-api", //测试
	  baseUrl :'/sygapi',//生产
	  // #endif
	// #ifdef APP-PLUS
		// baseUrl: "http://192.168.1.95:48080/app-api",
		baseUrl: "http://114.215.188.198:6808",
		
	// #endif
	poserUrl:'https://bxcs.boxuehao.cn/bxcs',
	api:{
		phoneLoginV2:"/api/phoneLoginV2", //密码登录post
		getMsgCode:"/api/msgCode/getMsgCode", //获取验证码get
		getAdvertising:"/api/advertising/getAdvertising", //获取banner广告post  layoutPosition 首页顶部 APP_HomeTop,课程顶部 APP_CourseTop,直播顶部 APP_LiveTop,商机顶部 APP_ProjectTop
		creatememberV1:"/api/creatememberV1",//注册会员post
		myMemberInfo:"/api/member/myMemberInfo", //用户信息
		homeMenu:"/api/homemenu/loadAppHomeMenuV1", //首页查询菜单
		indexSwiper:"/api/homepage/apiLoadAppHomePageDTOV1", //查询金刚区
		getStartupImgConfig:"/api/config/getStartupImgConfig", //查询分割条
		apiGetPostInformation:"/api/postinformation/apiGetPostInformation", //查询资讯post
		apiGetPostInfomationDetail:"/api/postinformation/apiGetPostInfomationDetailV1", //查询资讯详情post
		updateWatchAppHomePageTime:"/api/homepage/updateWatchAppHomePageTime", //首页视频记录post
		apiGetAppHomePageDtatil:"/api/homepage/apiGetAppHomePageDtatil", //首页视频详情post
		createcollect:"/api/member/collect/createcollect", //创建收藏post
		cancelcollect:"/api/member/collect/cancelcollect", //取消收藏post
		createup:"/api/member/up/createup", //创建点赞post
		cancelup:"/api/member/up/cancelup", //取消取消post
		getIndexLiving:"/api/live/getIndexLiving", //获取正在直播post
		liveVideopage:"/api/liveVideo/page", //获取数字人直播post
		liveVideoHeartbeat:"/api/redisUtil/liveVideoHeartbeat", //直播视频心跳get
		liveTransferHeartbeat:"/api/redisUtil/liveTransferHeartbeat", //观看直播视频心跳get
		liveHistoryHeartbeat:"/api/redisUtil/liveHistoryHeartbeat", //观看直播录播视频心跳get
		getLivePlay:"/api/live/getLivePlay", //获取直播播放地址get
		getLiveNumber:"/api/live/getLiveNumber", //获取直播人数get
		getLiveDetail:"/api/live/getLiveDetail", //获取直播间详情get
		videoTransferHeartbeat:"/api/redisUtil/videoTransferHeartbeat", //观看视频心跳get
		getPayConfig:"/api/config/getPayConfig" ,//支付配置post
		getMyApplyList:"/api/bizApplyDeposit/getMyApplyList", //获取申请列表post
		submitBizApplyDeposit:"/api/bizApplyDeposit/submitBizApplyDeposit" ,//提交押金post
		getBizDetailById:"/api/bizApplyDeposit/getBizDetailById" ,//押金详情post
		upLoadFile:"/api/file/upLoadFile" ,//图片上传post
		updateBizApplyDeposit:"/api/bizApplyDeposit/updateBizApplyDeposit" ,//更新参会押金状态post
		getLivePage:"/api/live/getLivePage", //获取全部直播数据post
		loadMemberInfo:"/api/member/loadMemberInfo", //获取用户详细信息get
		getMemberPhone:"/api/member/getMemberPhone" ,//获取用户电话get
		apiEditMemberPortrait:"/api/member/apiEditMemberPortrait" ,//修改头像post
		editMemberInfoV1:"/api/member/editMemberInfoV1", //用户名修改post
		apiEditMemberPhone:"/api/member/apiEditMemberPhone" ,//手机号修改post
		apiEditMemberPwd:"/api/member/apiEditMemberPwd" ,//密码修改post
		deleteMember:"/api/member/deleteMember" ,//账号注销post
		selectMyFocusCompany:"/api/member/MemberCompanyFocus/selectMyFocusCompany" ,//项目列表
		selectDefaultCompany:"/api/member/MemberCompanyFocus/selectDefaultCompany" ,//切换项目
		createFocus:"/api/member/MemberCompanyFocus/createFocus", //添加项目
		removeFocusCompany:"/api/member/MemberCompanyFocus/removeFocusCompany" ,//删除项目
		getPublishedList:"/api/protocol/getPublishedList" ,//获取协议列表
		protocoldetail:"/api/protocol/detail" ,//获取协议详情
		getOnLineViewLiveMemberList:"/api/live/getOnLineViewLiveMemberList" ,//获取直播人员
		getRecentLiveHistoryList:"/api/liveHistory/getRecentLiveHistoryList" ,//获取历史直播列表post
		getMemberLiveViewing:"/api/liveViewing/getMemberLiveViewing" ,//获取直播观看成员详情post
		createReport:"/api/report/createReport" ,//创建举报post
		getcoursess:"/api/course/getcoursess" ,//获取课程列表post
		getcoursedetai:"/api/course/getcoursedetai" ,//获取课程详情post
		getcourseperiods:"/api/courseperiod/getcourseperiods" ,//获取课时列表post
		getcoursedetai:"/api/course/getcoursedetail", //获取课程详情post
		getVideoPlayAuth:"/api/member/getVideoPlayAuth", //获取视频播放授权接口post
		selectMyPlayLive:"/api/member/selectMyPlayLive" ,//查询我的直播间状态get
		getLivePush:"/api/live/getLivePush" ,//直播推流地址获取get
		apiStartLive:"/api/live/apiStartLiveV1" ,//开始直播post
		apiCreateLiveHistory:"/api/liveHistory/apiCreateLiveHistory" //创建直播记录post
	},
	
	ajax: async function(obj) {
		try {
			// 直接使用存储的token
			const token = uni.getStorageSync('sygtoken')
			// console.log(token)
			obj.header = obj.header || {}
			obj.header.Authorization = token ? token : ''
			obj.header.identification = 'saas_syg'
			obj.header.os = this.getDeviceType()
			
			// 自动获取并添加companyId
			if ((obj.method === 'POST'||obj.method === 'GET') && typeof obj.data === 'object') {
				// 获取公司ID：优先从全局获取，如没有则从缓存获取
				let companyId = '';
				try {
					    var id=uni.getStorageSync('defaultCompany') || ''
						companyId = id.companyId;
					
				} catch (e) {
					console.error('获取companyId失败', e);
					companyId = '';
				}
				
				// 如果请求数据中没有companyId，且此接口通常需要companyId，则自动添加
				const needsCompanyId = obj.url.includes('homemenu') || 
					obj.url.includes('homepage') || 
					obj.url.includes('config') || 
					obj.url.includes('live') || 
					obj.url.includes('liveVideo') ||
					obj.url.includes('postinformation');
				
				// 对于POST请求，检查是否需要自动添加companyId
				if (needsCompanyId) {
					if (!obj.data.companyId) {
						obj.data.companyId = companyId;
						// console.log('自动添加companyId:', companyId,obj.data);
					}
				}
				
				// 对于需要在页面参数中传递companyId的接口，自动添加
				if (obj.url.includes('getAdvertising')) {
					// 确保不覆盖已有参数
					obj.data = obj.data || {};
				}
			}
			// console.log('请求参数',obj)
			// 使用 Promise 包装请求
			return new Promise((resolve, reject) => {
				uni.request({
					timeout: 30000,
					url: this.baseUrl + obj.url,
					method: obj.method || 'GET',
					data: obj.data,
					header: {
						'Content-Type': 'application/json',
						'Authorization': obj.header.Authorization,
						'identification':obj.header.identification,
						'os': obj.header.os
					},
					success: (res) => {
						// 处理H5平台返回数组的情况
						const response = Array.isArray(res) ? res[1] : res
						
						if (response.data.code == 1000) {
							// 处理切换项目成功的情况，更新defaultCompany
							if ((obj.url === this.api.createFocus||obj.url === this.api.selectDefaultCompany) && response.data.data) {
								// 获取当前的项目列表
								this.ajax({
									url: this.api.selectMyFocusCompany,
									method: 'GET',
									success: (companyRes) => {
										if (companyRes.code === 1000 && companyRes.data && companyRes.data.items && companyRes.data.items.items) {
											// 找到被选中的项目
											const selectedCompany = companyRes.data.items.items.find(item => item.selected === 1);
											if (selectedCompany) {
												const defaultCompany = {
													companyId: selectedCompany.companyId,
													companyName: selectedCompany.companyName,
													logoImgUrl: selectedCompany.logoImgUrl
												};
												// 更新缓存
												uni.setStorageSync('defaultCompany', defaultCompany);
												console.log('已更新defaultCompany:', defaultCompany);
											}
										}
									}
								});
							}
							
							obj.success && obj.success(response.data)
							resolve(response.data)
						} else if (response.data.code == 0) {
							obj.success && obj.success(response.data)
							resolve(response.data)
						} else if (response.data.code === 401) {
							// 401错误，清除token并返回到my页面
							console.error('认证失败，请重新登录')
							uni.removeStorageSync('sygtoken')
							
							// 避免重复跳转
							const pages = getCurrentPages()
							const currentPage = pages[pages.length - 1]
							if (currentPage.route !== 'pages/investment/index') {
								uni.showToast({
									title: '登录已过期，请重新登录',
									icon: 'none',
									duration: 2000
								})
								uni.switchTab({
									url: '/pages/sign/sign'
								})
							}
							reject(response.data)
						} else if (response.data.code === 403) {
							// 403错误，处理单点登录冲突，执行退出登录操作
							console.error('账号在其他设备登录，您已被迫下线')
							// 清除登录状态
							uni.removeStorageSync('sygtoken')
							uni.removeStorageSync('defaultCompany')
							uni.removeStorageSync('syguser')
							getApp().globalData.login = false
							
							// 避免重复跳转
							const pages = getCurrentPages()
							const currentPage = pages[pages.length - 1]
							if (currentPage.route !== 'pages/investment/index') {
								uni.showToast({
									title: '账号在其他设备登录，您已被迫下线',
									icon: 'none',
									duration: 2000
								})
								uni.reLaunch({
									url: '/pages/sign/sign'
								})
							}
							reject(response.data)
						} else {
							uni.showToast({
								title: response.data.msg || '请求失败',
								icon: 'none',
								mask: true
							})
							obj.success && obj.success(response.data)
							resolve(response.data)
						}
					},
					fail(err) {
						obj.loading && uni.hideLoading()
						// 处理请求超时
						if(err.errMsg.includes('timeout') || err.errMsg.includes('-1001')) {
							uni.showToast({
								title: '请求超时，请检查网络',
								icon: 'none',
								duration: 2000
							})
						}
						// 处理403错误
						else if(err.statusCode === 403 || (err.errMsg && err.errMsg.includes('403'))) {
							console.error('账号在其他设备登录，您已被迫下线')
							// 清除登录状态
							uni.removeStorageSync('sygtoken')
							uni.removeStorageSync('defaultCompany')
							uni.removeStorageSync('syguser')
							getApp().globalData.login = false
							
							// 避免重复跳转
							const pages = getCurrentPages()
							const currentPage = pages[pages.length - 1]
							if (!currentPage || currentPage.route !== 'pages/investment/index') {
								uni.showToast({
									title: '账号在其他设备登录，您已被迫下线',
									icon: 'none',
									duration: 2000
								})
								uni.reLaunch({
									url: '/pages/sign/sign'
								})
							}
						}
						obj.fail && obj.fail(err)
						reject(err)
					},
					complete(res) {
						console.log('请求完成:', res)
						// 处理HTTP状态码为403的情况
						if (res && res.statusCode === 403) {
							console.error('账号在其他设备登录，您已被迫下线')
							// 清除登录状态
							uni.removeStorageSync('sygtoken')
							uni.removeStorageSync('defaultCompany')
							uni.removeStorageSync('syguser')
							getApp().globalData.login = false
							
							// 避免重复跳转
							const pages = getCurrentPages()
							const currentPage = pages[pages.length - 1]
							if (!currentPage || currentPage.route !== 'pages/investment/index') {
								uni.showToast({
									title: '账号在其他设备登录，您已被迫下线',
									icon: 'none',
									duration: 2000
								})
								uni.reLaunch({
									url: '/pages/sign/sign'
								})
							}
						}
					}
				})
			})
		} catch(e) {
			console.error('请求失败:', e)
			obj.fail && obj.fail(e)
			throw e
		}
	},
	info: function(obj) {
		uni.showLoading({
			mask: true
		})
		var token=uni.getStorageSync('sygtoken')
		uni.request({
			url: this.baseurl + this.api.info,
			method: 'POST',
			header: {
			  'Content-Type': 'application/x-www-form-urlencoded',
			  'x-api-key':token
			},
			success(res) {
				uni.hideLoading()
				if (res.data.code == 200) {
					uni.setStorageSync('info',res.data.data)
					
				}
				else {
					if(res.data.code==401){
						uni.showToast({
							title:res.data.message,
							icon:'none',
							mask:true
						})
						uni.removeStorageSync('info')
						uni.removeStorageSync('sygtoken')
						getApp().globalData.login=false
						uni.switchTab({
							url:'/pages/sign/sign'
						})
						return;
					}
					uni.showToast({
						title:res.data.message,
						icon:'none',
						mask:true
					})
					obj.success && obj.success(res.data)
				}
			},
			fail(res) {
				console.log(res)
				obj.loading && uni.hideLoading()
				obj.fail && obj.fail(res)
			},
			complete(res) {
				console.log(res)
				// 处理HTTP状态码为403的情况
				if (res && res.statusCode === 403) {
					console.error('账号在其他设备登录，您已被迫下线')
					// 清除登录状态
					uni.removeStorageSync('sygtoken')
					uni.removeStorageSync('defaultCompany')
					uni.removeStorageSync('syguser')
					getApp().globalData.login = false
					
					// 避免重复跳转
					const pages = getCurrentPages()
					const currentPage = pages[pages.length - 1]
					if (!currentPage || currentPage.route !== 'pages/investment/index') {
						uni.showToast({
							title: '账号在其他设备登录，您已被迫下线',
							icon: 'none',
							duration: 2000
						})
						uni.reLaunch({
							url: '/pages/sign/sign'
						})
					}
				}
			}
		})
	},
	// 时间戳
	timestampToDate(timestamp) {
		var date = new Date(timestamp); // 如果timestamp是数值，则直接使用，否则需要转换
		var year = date.getFullYear();
		var month = (date.getMonth() + 1).toString().padStart(2, '0');
		var day = date.getDate().toString().padStart(2, '0');
		return `${year}-${month}-${day}`;
	},
	// 文件上传方法
	uploadFile(options = {}) {
		const token = uni.getStorageSync('sygtoken');
		const loading = options.showLoading !== false;
		
		if (loading) {
			uni.showLoading({
				title: options.loadingText || '上传中...',
				mask: true
			});
		}
		
		return new Promise((resolve, reject) => {
			uni.uploadFile({
				url: this.baseUrl + this.api.upLoadFile,
				filePath: options.filePath,
				name: options.name || 'file',
				formData: options.formData || {},
				header: {
					'Authorization': token ? token : '',
					'identification': 'saas_syg',
					'os': this.getDeviceType()
				},
				success: (uploadFileRes) => {
					if (loading) uni.hideLoading();
					try {
						let result;
						if (typeof uploadFileRes.data === 'string') {
							result = JSON.parse(uploadFileRes.data);
						} else {
							result = uploadFileRes.data;
						}
						// console.log(result)
						if (result.code === 1000) {
							resolve(result); // 返回完整的 result 对象
						} else {
							uni.showToast({
								title: result.msg || '上传失败',
								icon: 'none',
								duration: 2000
							});
							reject(result);
						}
					} catch(e) {
						console.error('上传解析错误:', e);
						uni.showToast({
							title: '上传失败: 数据解析错误',
							icon: 'none',
							duration: 2000
						});
						reject(e);
					}
				},
				fail: (err) => {
					if (loading) uni.hideLoading();
					console.error('上传失败:', err);
					uni.showToast({
						title: '上传失败: ' + (err.errMsg || '网络错误'),
						icon: 'none',
						duration: 2000
					});
					reject(err);
				}
			});
		});
	},
	// 添加文件上传方法
	upload(options = {}) {
		const token = uni.getStorageSync('sygtoken');
		
		return new Promise((resolve, reject) => {
			uni.uploadFile({
				url: this.baseUrl + this.api.uploadImg,
				filePath: options.filePath,
				name: 'file',
				// formData: {
				// 	path: options.path || 'default'  // 上传路径，默认为 default
				// },
				header: {
					'Authorization': token ? token : ''
				},
				success: (uploadFileRes) => {
					try {
						const res = JSON.parse(uploadFileRes.data);
						resolve(res);
					} catch(e) {
						reject(new Error('上传失败'));
					}
				},
				fail: (err) => {
					reject(err);
				}
			});
		});
	},
	// 获取设备类型：0安卓，1苹果
	getDeviceType() {
		try {
			const systemInfo = uni.getSystemInfoSync();
			const platform = systemInfo.platform.toLowerCase();
			
			if (platform === 'android') {
				return '0';
			} else if (platform === 'ios') {
				return '1';
			} else {
				return '0'; // 默认返回安卓
			}
		} catch (e) {
			console.error('获取设备类型失败:', e);
			return '0'; // 获取失败时默认返回安卓
		}
	},
}
export default ajax
