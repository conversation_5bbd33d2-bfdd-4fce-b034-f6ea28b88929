<script>
	// 静默更新
	import silentlyAppUpdate from '@/uni_modules/wu-app-update/js-sdk/silentlyAppUpdate.js';
	export default {
		onLaunch: function() {
			
			console.log('App Launch')
			// #ifdef APP-PLUS	
						// 获取系统信息，用于适配屏幕
			const systemInfo = uni.getSystemInfoSync();
			console.log('设备信息:', systemInfo);
			// 修复平板模式下显示问题
			if (
			// systemInfo.windowWidth >= 768&& 
			 (systemInfo.model=='P16'||systemInfo.model=='PG12')) {
				plus.screen.lockOrientation('landscape-primary')
				// 平板模式下需要调整视图
				console.log('检测到平板设备');
				this.globalData.isTablet = true;
				this.globalData.deviceWidth = systemInfo.windowWidth;
				this.globalData.deviceHeight = systemInfo.windowHeight;
				uni.reLaunch({
					url:'/pages/growthPlanhengping/growthPlan'
				})
			} else {
				this.globalData.isTablet = false;
				var w = plus.webview.open(
				'hybrid/html/advertise/advertise.html',
				'本地地址',
				uni.getSystemInfoSync().platform == 'android' ? {
					top: 0,
					bottom: 0,
					zindex: 999
				} : 
				{ top: -60, bottom: 0, zindex: 999 },
				'fade-in',
				500
			);
			
			//设置定时器，4s后关闭启动广告页
			setTimeout(function() {
				plus.webview.close(w);
			}, 5000);
			plus.screen.lockOrientation('portrait-primary')
			}
			
			// const isAgreePrivacy = uni.getStorageSync('isAgreePrivacy');
			

			  var agree = plus.runtime.isAgreePrivacy();
			  console.log(agree)
			  if (agree) {
				console.log('静默执行')
					silentlyAppUpdate();
			}else{
			
			}
			

			
			
			// #endif
	
			// uni.hideTabBar()	
			// uni.$u.toast('请阅读并同意用户协议和隐私政策');
			var token = uni.getStorageSync('sygtoken')
			if (token) {
				this.globalData.login = true;
				uni.switchTab({
					url:'/pages/investment/index'
				})
			} else {
				this.globalData.login = false;
			}
			
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		globalData: {
			login: false,
			isTablet: false,
			deviceWidth: 375,
			deviceHeight: 667
		}
	}
</script>

<style lang="scss">
	 /* #ifndef APP-PLUS-NVUE	 */
	@import "@/uni_modules/uview-ui/index.scss";
	@import './static/css/main.css';
	@import './static/css/zwyCss.css';
	@import './static/css/app.scss';
	@import './static/css/tablet-adaptive.css';
	::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
		color: transparent;
	}
	uni-page-head .uni-page-head__title{
		font-weight: 400 !important;
	}
	
	/* 添加全局屏幕适配 */
	page {
		width: 100%;
		height: 100%;
		font-size: 28upx;
		box-sizing: border-box;
	}
	
	/* 适配平板和大屏设备 */
	@media screen and (min-width: 768px) {
		page {
			width: 100%;
			height: 100%;
			max-width: 100%;
			font-size: 28upx; /* 保持字体大小一致 */
			box-sizing: border-box;
		}
		
		.content {
			width: 100%;
			margin: 0 auto;
		}
	}
	/* #endif */
	

	
</style>