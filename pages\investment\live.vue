<template>
	<view class="live-room-container" @click="toggleControlsVisible">
		<!-- 全屏视频播放器 - 使用DomVideoPlayer组件 -->
		<DomVideoPlayer
			id="videoPlayer"
			ref="domVideoPlayer"
			:src="videoUrl" 
			:autoplay="true"
			:controls="false"
			:muted="false"
			:isLoading="true"
			:objectFit="isLandscape ? 'contain' : 'contain'"
			@play="onVideoPlay"
			@pause="onVideoPause"
			@ended="onVideoEnded"
			@error="onVideoError"
			@sourceEnded="onSourceEnded"
			:class="['full-video-player', isLandscape ? 'landscape-video' : '', isLandscape && showGiftPopup ? 'video-shrink' : '', isInitialLoading ? 'hidden-video' : '']"
		/>
		<!-- 顶部悬浮控制栏 -->
		<view class="top-controls" :class="{'landscape-top-controls': isLandscape, 'controls-hidden': !isControlsVisible}" @touchstart="showControlsOnTouch">
			<view class="gradient-overlay" v-if="!isLandscape"></view>
			
			<view class="live-info" :class="{'landscape-info': isLandscape}">
				<view class="back-btn" @click="goBack">
					<u-icon name="arrow-left" color="#FFFFFF" size="22"></u-icon>
				</view>
				<view class="live-author-info" :class="{'landscape-author-info': isLandscape}">
					<view class="live-author-name">{{ videoInfo.name || '精彩内容' }}</view>
					<view class="live-title">
						<text>{{ videoInfo.author || '勃学负责人' }}</text>
						<view class="viewer-count" v-if="viewerCount > 0">
							<u-icon name="eye" color="#FFFFFF" size="16"></u-icon>
							<text>{{ formatCount(viewerCount) }}</text>
				</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 右侧悬浮控制栏 -->
		<view class="right-controls" :class="{'controls-hidden': !isControlsVisible}" v-if="!isLandscape">
			<view class="control-icon-item" @click="toggleLike">
				<view class="icon-wrapper" :class="{'active': isLiked}">
					<image
						:src="isLiked ? '/static/image/dianzan.png' : '/static/image/quxiaodianzan.png'"
						mode="aspectFit"
						style="width: 36rpx; height: 36rpx;"
					/>
				</view>
				<text>{{ formatCount(likeCount) }}</text>
			</view>
			<view class="control-icon-item" @click="showGiftPanel">
				<view class="icon-wrapper">
					<u-icon name="gift" color="#FFFFFF" size="22"></u-icon>
				</view>
				<text>礼物</text>
			</view>
		</view>
		
		<!-- 视频播放按钮 - 暂停时显示 -->
		<view class="play-btn-center" v-if="!isPlaying && !isInitialLoading" @click="togglePlay">
			<view class="play-btn-icon">
				<u-icon name="play-right-fill" color="#FFFFFF" size="50"></u-icon>
			</view>
		</view>
		
		<!-- 推荐视频侧边抽屉 -->
		<view class="recommend-drawer" :class="{'drawer-open': isRecommendOpen}" @touchstart.stop @click.stop>
			<view class="drawer-header" :style="{'padding-top': statusBarHeight + 44 + 'px'}">
				<text class="drawer-title">特别推荐</text>
				<view class="drawer-close" @click.stop="toggleRecommend">
					<u-icon name="close" color="#FFFFFF" size="30"></u-icon>
				</view>
			</view>
			
			<scroll-view scroll-y class="recommend-list" @touchstart.stop @click.stop>
				<view 
					class="recommend-item" 
					v-for="(item, index) in relatedVideos" 
					:key="index"
					@click.stop="playRelatedVideo(item)"
				>
					<view class="recommend-cover-container">
						<image class="recommend-cover" :src="item.videoThumbnailUrl" mode="widthFix"></image>
					</view>
					<view class="recommend-info">
						<view class="recommend-name">{{item.name}}</view>
						<view class="recommend-meta">
							<text class="recommend-author">{{item.author || '未知'}}</text>
							<text class="recommend-duration" v-if="item.duration">{{formatDuration(item.duration)}}</text>
						</view>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-recommend" v-if="!loading && relatedVideos.length === 0">
					<u-icon name="list" color="#666666" size="50"></u-icon>
					<text class="empty-text">暂无相关内容</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 评论弹窗 -->
		<u-popup mode="bottom" :show="showCommentPopup" @close="closeComments" :safeAreaInsetBottom='false'  round="20">
			<view class="comment-container">
				<view class="comment-header">
					<text class="comment-title">评论 {{ formatCount(commentCount) }}</text>
					<view class="comment-close" @click="closeComments">
						<u-icon name="close" color="#333333" size="24"></u-icon>
					</view>
				</view>
				<scroll-view scroll-y class="comment-list">
					<view class="comment-item" v-for="(item, index) in commentList" :key="index">
						<view class="comment-content">
							<view class="comment-user">{{ item.userName }}</view>
							<view class="comment-text">{{ item.content }}</view>
							<view class="comment-time">{{ item.time }}</view>
						</view>
						<!-- 评论区点赞按钮 -->
						<view class="comment-like" @click="likeComment(index)">
							<image
								:src="item.isLiked ? '/static/image/dianzan.png' : '/static/image/quxiaodianzan.png'"
								mode="aspectFit"
								style="width: 28rpx; height: 28rpx; margin-right: 4rpx;"
							/>
							<text :class="{'liked': item.isLiked}">{{ formatCount(item.likes) }}</text>
						</view>
					</view>
					
					<!-- 空状态 -->
					<view class="empty-comment" v-if="commentList.length === 0">
						<u-icon name="chat" color="#666666" size="50"></u-icon>
						<text class="empty-text">暂无评论，快来抢沙发吧</text>
					</view>
				</scroll-view>
				<view class="comment-input-area">
					<view class="comment-input-box">
						<input type="text" placeholder="说点什么..." v-model="commentText" />
					</view>
					<view class="comment-send-btn" :class="{'active': commentText.trim()}" @click="sendComment">
						发送
					</view>
				</view>
			</view>
		</u-popup>
		
		<!-- 播放结束提示 -->
		<view class="play-end-tips" v-if="isPlayEnded">
			<view class="play-end-content">
				<view class="play-end-title" ref="endTitleRef">{{ endTitleText }}</view>
				<!-- <view class="play-end-thumbnail" v-if="videoInfo.videoThumbnailUrl">
					<image :src="videoInfo.videoThumbnailUrl" mode="widthFix"></image>
					<view class="play-end-thumbnail-mask"></view>
				</view> -->
				<view class="play-end-btns">
					<!-- <view class="play-end-btn replay" @click="replayVideo">
						<u-icon name="reload" color="#FFFFFF" size="18"></u-icon>
						<text>重新播放</text>
					</view> -->
					<!-- <view class="play-end-btn next" @click="playNextVideo" v-if="relatedVideos.length > 0">
						<u-icon name="rewind-right-fill" color="#FFFFFF" size="18"></u-icon>
						<text>下一视频</text>
					</view> -->
					<view class="play-end-btn back" ref="endBtnRef" @click="goBack">
						<u-icon name="home" color="#FFFFFF" size="18"></u-icon>
						<text>返回首页</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载层 -->
		<view class="loading-layer" v-if="isInitialLoading">
			<!-- <u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle" iconSize='30'
				 :loading="isInitialLoading">
			</u-loading-page> -->
		</view>
		
		<!-- 横屏提示动画 -->
		<view class="rotate-toast" v-if="showRotateHint">
			<view class="rotate-animation">
				<view class="phone-container">
					<view class="phone">
						<view class="phone-inner"></view>
						<view class="phone-button"></view>
					</view>
				</view>
			</view>
			<text class="rotate-text">旋转手机获得更好体验</text>
		</view>
		
		<u-toast ref="uToast"></u-toast>
		
		<!-- H5 分享弹窗 -->
		<!-- #ifdef H5 -->
		<u-popup @close="closeSharePopup" :show="showSharePopup" mode="bottom" border-radius="20">
			<view class="share-popup">
				<view class="share-title">分享至</view>
				<view class="share-options">
					<view class="share-item" @click="copyLink">
						<image src="/static/image/copyurl.png" mode="aspectFit"></image>
						<text>复制链接</text>
					</view>
				</view>
				<view class="share-cancel" @click="closeSharePopup">取消</view>
			</view>
		</u-popup>
		<!-- #endif -->
		
		<!-- 添加横屏底部控制栏 -->
		<view class="bottom-controls" v-if="isLandscape" :class="{'controls-hidden': !isControlsVisible}">
			<view class="bottom-gradient"></view>
			<view class="bottom-buttons">
				<view class="bottom-btn" @click="toggleLike">
					<view class="bottom-icon-wrapper" :class="{'active': isLiked}">
						<image
							:src="isLiked ? '/static/image/dianzan.png' : '/static/image/quxiaodianzan.png'"
							mode="aspectFit"
							style="width: 32rpx; height: 32rpx;"
						/>
					</view>
					<text>{{ formatCount(likeCount) }}</text>
				</view>
				<view class="bottom-btn" @click="showGiftPanel">
					<view class="bottom-icon-wrapper">
						<u-icon name="gift" color="#FFFFFF" size="22"></u-icon>
					</view>
					<text>礼物</text>
				</view>
			</view>
		</view>
		
		<!-- 修改礼物弹窗组件 - 在横屏模式下改为侧边面板 -->
		<u-popup v-if="!isLandscape" mode="bottom" :show="showGiftPopup" @close="closeGiftPanel" :safeAreaInsetBottom='false' round="20">
			<view class="gift-container">
				<view class="gift-header">
					<text class="gift-title">送礼物</text>
					<view class="gift-close" @click="closeGiftPanel">
						<u-icon name="close" color="#fff" size="24"></u-icon>
					</view>
				</view>
				
				<scroll-view scroll-y class="gift-grid" show-scrollbar="false">
					<view class="gift-grid-inner">
						<view 
							class="gift-item" 
							v-for="(item, index) in giftList" 
							:key="index"
							:class="{'gift-selected': selectedGift && selectedGift.id === item.id}"
							@click="selectGift(item)"
						>
							<image class="gift-image" :src="item.url" mode="aspectFit"></image>
							<text class="gift-name">{{item.name}}</text>
						</view>
					</view>
				</scroll-view>
				
				<view class="gift-button-row">
					<view class="gift-send-btn" :class="{'active': selectedGift}" @click="sendGift">
						赠送礼物
					</view>
				</view>
			</view>
		</u-popup>
		
		<!-- 横屏模式下的礼物侧边面板 -->
		<view v-if="isLandscape" class="landscape-gift-panel" :class="{'panel-open': showGiftPopup}" @click.stop>
			<view class="landscape-gift-content">
				<view class="gift-header">
					<text class="gift-title">送礼物</text>
					<view class="gift-close" @click="closeGiftPanel">
						<u-icon name="close" color="#fff" size="24"></u-icon>
					</view>
				</view>
				
				<scroll-view scroll-y class="landscape-gift-grid" show-scrollbar="false">
					<view class="gift-grid-inner landscape">
						<view 
							class="gift-item" 
							v-for="(item, index) in giftList" 
							:key="index"
							:class="{'gift-selected': selectedGift && selectedGift.id === item.id}"
							@click="selectGift(item)"
						>
							<image class="gift-image" :src="item.url" mode="aspectFit"></image>
							<text class="gift-name">{{item.name}}</text>
						</view>
					</view>
				</scroll-view>
				
				<view class="gift-button-row">
					<view class="gift-send-btn" :class="{'active': selectedGift}" @click="sendGift">
						赠送礼物
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加点赞动画组件 - 根据横竖屏使用不同GIF -->
		<view class="like-animation-container" v-if="showLikeAnimation">
			
			<image class="like-gif" :src="isLandscape ? 'https://bxcs.boxuehao.cn/bxcs/hengdianzan.gif' : 'https://bxcs.boxuehao.cn/bxcs/dianzan.gif'" mode="widthFix"></image>
		</view>
		
		<!-- 横屏弹幕式礼物动画 -->
		<view class="landscape-gift-danmaku-container" v-if="isLandscape">
			<view 
				v-for="(danmaku, index) in giftDanmakus" 
				:key="danmaku.id"
				class="gift-danmaku"
				:style="{
					top: (10 + (index * 10)) + '%',
					animationDelay: (index * 0.5) + 's'
				}"
			>
				<view class="danmaku-content">
					<text class="danmaku-sender">{{danmaku.sender}}</text>
					<text class="danmaku-text">赠送</text>
					<image class="danmaku-gift-icon" :src="danmaku.gift.url" mode="aspectFit"></image>
					<text class="danmaku-gift-name">{{danmaku.gift.name}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import DomVideoPlayer from '@/components/DomVideoPlayer/livePlayer.vue'
	import UniShare from 'uni_modules/uni-share/js_sdk/uni-share.js';
	
	const uniShare = new UniShare();
	
	export default {
		components: {
			DomVideoPlayer
		},
		data() {
			return {
				videoId: null,
				videoInfo: {
					id: null,
					name: '',
					url: '',
					flvUrl: '',
					rtmpUrl: '',
					hlsUrl: '',
					videoThumbnailUrl: '',
					author: '',
					authorAvatar: '',
					duration: 0,
					businessName: '',
					musicName: ''
				},
				videoUrl: '', // 缓存视频URL
				initialProgressSet: false, // 标记是否已设置初始进度
				loading: true,
				isInitialLoading: true,
				isPlaying: false,
				isPlayEnded: false,
				relatedVideos: [],
				isRecommendOpen: false,
				isLiked: false,
				isFollowed: false,
				likeCount: 0,
				commentCount: 0,
				showCommentPopup: false,
				commentText: '',
				commentList: [],
				videoPlayer: null,
				statusBarHeight: 20, // 状态栏高度，默认值
				hasShownErrorToast: false, // 用于控制错误提示只显示一次
				showSharePopup: false, // H5 分享弹窗显示状态
				currentUrl: '', // 当前页面完整URL用于分享
				poserPath: '', // 海报路径
				posterConfig: null, // 海报配置
				posterUrl: '', // 海报背景URL
				isLandscape: false, // 是否是横屏模式
				landscapeTimer: null, // 横屏模式下的自动隐藏计时器
				isControlsVisible: true, // 控制栏是否可见
				windowWidth: 0,
				windowHeight: 0,
				showRotateHint: false, // 是否显示旋转提示
				videoIsLandscape: false, // 视频是否是横屏尺寸
				heartbeatTimer: null, // 心跳定时器
				heartbeatInterval: 30, // 心跳间隔秒数
				playWay: 0, // 播放方式：0为竖屏，1为横屏
				companyId: '', // 公司ID，可从全局配置获取
				viewerCount: 0, // 实时观看人数
				controlsTimer: null, // 控制栏自动隐藏计时器
				isPaused: false, // 添加暂停状态标记
				
				// 添加礼物相关数据
				showGiftPopup: false, // 礼物面板显示状态
				giftList: [], // 礼物列表
				selectedGift: null, // 选中的礼物
				giftAnimations: [], // 礼物动画队列
				isShowingGift: false, // 是否正在展示礼物
				showLikeAnimation: false, // 是否显示点赞动画，
				
				// 添加新的礼物动画相关数据
				giftDanmakus: [], // 横屏弹幕式礼物数据
				fakeSenderTimer: null, // 假人送礼定时器
				fakeNames: [
    "用户7**12",
    "勃学***",
    "张**",
    "湖南**",
    "勃学铁粉**",
    "用户_ab***21",
    "李**老板",
    "看好勃学超市",
    "广东**用户",
    "一起发财888",
    "王**",
    "支持勃学666",
    "用户**24",
    "浙江**小老板",
    "A努力奋斗中",
    "明天就加盟**",
    "刘**姐",
    "user***168",
    "四川**哥",
    "就想搞钱**",
    "勃学老用户",
    "陈**",
    "新用户**9527",
    "上海**",
    "诚信赢天下",
    "用户186****335",
    "赵**先生",
    "勃学超市yyds",
    "爱拼才会赢",
    "河南**老乡",
    "游客**88",
    "就等勃学开到我们这",
    "奋斗的小青年",
    "山东**",
    "黄**",
    "用户_m**g",
    "招财进宝",
    "江苏**",
    "想加盟的周**",
    "WX用户**77",
    "心向阳光",
    "勃学发大财",
    "北京朝阳**",
    "用户36**58",
    "明天会更好",
    "福建**用户",
    "生意兴隆**",
    "吴**",
    "追梦人**",
    "奋斗的青春**"
  ],
				// 添加直播结束检测相关数据
				streamErrorCount: 0,
				maxStreamErrors: 3,
				streamCheckTimer: null,
				noFramesDetected: false,
				lastPlaybackTime: 0,
				stuckFrameCounter: 0,
				
				// 添加直播状态检测相关变量
				liveStatusCheckTimer: null,
				isLiveEnded: false, // 标记直播是否真正结束
				isLiveError: false, // 标记是否是直播错误
				endTitleText: '直播结束', // 结束标题文本
				
				// 添加礼物动画相关数据
				showGiftAnimation: false, // 是否显示礼物Lottie动画
				giftLottieUrl: '', // Lottie JSON文件URL
				
				// 直播状态持久化
				liveStateData: null, // 保存直播状态的对象
				leaveTimestamp: 0, // 添加记录离开时间的时间戳
				leavePosition: 0, // 添加记录离开时的播放位置
				isFromGiftAnimation: false, // 添加标记是否从礼物动画页面返回
			}
		},
		computed: {
			// 格式化视频描述，支持话题标签高亮显示
			videoDescription() {
				if (!this.videoInfo.businessName) return '';
				// 将#话题#替换为带样式的span
				return this.videoInfo.businessName.replace(/#([^#]+)#/g, '<span style="color: #00C1CC; font-weight: bold;">#$1#</span>');
			}
		},
		onResize() {
			// 检测屏幕方向
			this.checkOrientation();
		},
		onLoad(options) {
			// 重置所有定时器状态，确保没有遗留的定时器
			this.heartbeatTimer = null;
			this.controlsTimer = null;
			this.landscapeTimer = null;
		
			// 获取状态栏高度
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 20;
			
			// 解锁屏幕方向，允许横屏
			// #ifdef APP-PLUS
			plus.screen.unlockOrientation();
			// #endif
			
			// 注册窗口大小变化监听
			uni.onWindowResize(this.windowResizeCallback);
			
			// 尝试获取公司ID
			try {
				this.companyId = getApp().globalData.companyId || '';
			} catch (e) {
				console.error('获取公司ID失败:', e);
			}
			
			// 处理传递过来的视频数据
			if (options.videoData) {
				try {
					// 解析视频数据
					const videoData = JSON.parse(decodeURIComponent(options.videoData));
					console.log('解析后的视频数据:', videoData);
					
					// 设置播放方式（横竖屏）
					this.playWay = videoData.playWay || 0;
					this.isLandscape = this.playWay === 1;
					console.log('播放方式:', this.playWay, '是否横屏:', this.isLandscape);
					
					// 视频URL信息
					let flvUrl = '';
					let rtmpUrl = '';
					let hlsUrl = '';
					
					// 检查不同的数据结构
					if (videoData.data && typeof videoData.data === 'object') {
						// 如果是包含在data字段中的格式
						flvUrl = videoData.data.FLV || '';
						rtmpUrl = videoData.data.RTMP || '';
						hlsUrl = videoData.data.HLS || '';
					} else {
						// 直接在根级别的格式
						flvUrl = videoData.FLV || '';
						rtmpUrl = videoData.RTMP || '';
						hlsUrl = videoData.HLS || '';
					}
					
					// 更新视频信息
					this.videoInfo = {
						id: videoData.id,
						name: videoData.name,
						url: videoData.url || '',
						flvUrl: flvUrl,
						rtmpUrl: rtmpUrl,
						hlsUrl: hlsUrl,
						videoThumbnailUrl: videoData.videoThumbnailUrl,
						author: videoData.author || '勃学负责人',
						businessName: videoData.description
					};
					
					console.log('处理后的视频信息:', this.videoInfo);
					
					this.videoId = videoData.id;
					
					// 先获取并缓存视频URL，避免重复调用
					this.videoUrl = this.getOptimalVideoUrl();
					
					// 处理相关推荐视频数据
					if (options.relatedVideos) {
						try {
							this.relatedVideos = JSON.parse(decodeURIComponent(options.relatedVideos));
						} catch (e) {
							console.error('解析相关视频数据失败:', e);
						}
					} else {
						// 如果没有传递相关推荐，则调用API获取
						// this.getRelatedVideos();
					}
					
					// 获取直播间详情
					this.getLiveDetail();
					
					// 开始心跳
					this.startHeartbeat();
					
					// 模拟数据
					this.likeCount = Math.floor(Math.random() * 10000) + 100;
					
					// 如果有初始播放位置，在视频准备好后设置进度
					if (videoData.currentProgress > 0) {
						// 监听视频初始化完成
						this.$nextTick(() => {
							const videoPlayer = this.$refs.domVideoPlayer;
							if (videoPlayer) {
								// 监听视频播放事件来设置初始位置
								const setInitialProgress = () => {
									if (videoPlayer.toSeek && !this.initialProgressSet) {
										console.log('设置视频初始播放位置:', videoData.currentProgress, '秒');
										videoPlayer.toSeek(videoData.currentProgress);
										this.initialProgressSet = true;
									}
								};
								
								// 监听play事件以确保视频已准备好
								videoPlayer.$on('ready', setInitialProgress);
								
								// 增加一个备用方案，如果没有ready事件
								setTimeout(setInitialProgress, 800);
							}
						});
					}
					
					// 延迟关闭加载状态，确保视频加载
					setTimeout(() => {
						this.isInitialLoading = false;
					}, 1000);
				} catch (e) {
					console.error('解析视频数据失败:', e);
					this.isInitialLoading = false;
				}
			} else {
				this.loading = false;
				this.isInitialLoading = false;
				uni.showToast({
					title: '视频ID不存在',
					icon: 'none'
				});
			}
			
			// 获取当前页面URL用于分享
			this.getCurrentPageUrl();
		},
		onShow() {
			console.log('页面显示，恢复资源');
			
			// 如果是从礼物动画页面返回，则不处理视频位置
			if (this.isFromGiftAnimation) {
				console.log('从礼物动画页面返回，不处理视频位置');
				this.isFromGiftAnimation = false; // 重置标记
			} 
			// 计算时间差并更新播放位置（仅针对非直播视频）
			else if (this.leaveTimestamp > 0 && !this.videoInfo.flvUrl && !this.videoInfo.hlsUrl && !this.videoInfo.rtmpUrl) {
				const timeDiff = (Date.now() - this.leaveTimestamp) / 1000; // 转换为秒
				const newPosition = this.leavePosition + timeDiff;
				
				console.log('应用离开时长:', timeDiff, '秒');
				console.log('计算的新播放位置:', newPosition, '秒');
				
				// 重置离开时间戳
				this.leaveTimestamp = 0;
				
				// 设置新的播放位置
				this.$nextTick(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && videoPlayer.toSeek && newPosition > 0) {
						console.log('设置新的播放位置:', newPosition);
						videoPlayer.toSeek(newPosition);
					}
				});
			}
			
			// 恢复播放，不再判断之前是否暂停
			this.resumeAllResources();
			this.isPaused = false;
			
			// 从Lottie页面返回时恢复状态
			this.restoreLiveState();
			
			// 如果存在视频播放器，强制播放
			this.$nextTick(() => {
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.play) {
					videoPlayer.play();
					this.isPlaying = true;
				}
			});
		},
		onHide() {
			console.log('页面隐藏，暂停资源');
			// 记录离开时间戳和当前播放位置
			this.leaveTimestamp = Date.now();
			const videoPlayer = this.$refs.domVideoPlayer;
			if (videoPlayer && typeof videoPlayer.currentTime === 'number') {
				this.leavePosition = videoPlayer.currentTime;
				console.log('记录离开时的播放位置:', this.leavePosition);
			}
			
			// 暂停资源，而不是清理
			this.pauseAllResources();
			this.isPaused = true;
			
			// 隐藏分享菜单
			this.$nextTick(function() {
				uniShare.hide();
			});
			
			return uniShare.isShow;
		},
		onUnload() {
			console.log('页面卸载，清理资源');
			// 完全清理所有定时器和心跳
			this.clearAllTimers();
			
			// 恢复竖屏锁定
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('portrait-primary');
			// #endif
			
			// 隐藏分享菜单
			this.$nextTick(function() {
				uniShare.hide();
			});
			
			// 移除窗口大小监听
			uni.offWindowResize(this.windowResizeCallback);
			
			return uniShare.isShow;
		},
		onBackPress({from}) {
			console.log('返回按钮按下，清理资源');
			// 停止所有定时器和心跳
			this.clearAllTimers();
			
			if (from == 'navigateBack') {
				this.$nextTick(function() {
					uniShare.hide();
				});
				return uniShare.isShow;
			}
		},
		mounted() {
			// 设置自动隐藏控制栏的定时器
			this.startAutoHideControls();
		},
		beforeDestroy() {
			console.log('组件销毁，清理资源');
			// 停止所有定时器和心跳
			this.clearAllTimers();
			
			// 移除监听
			uni.offWindowResize(this.windowResizeCallback);
		},
		methods: {
			// 获取最佳视频URL，根据设备和浏览器支持选择合适的视频源
			getOptimalVideoUrl() {
				console.log('获取最佳视频URL');
				
				// 判断环境和平台
				// #ifdef H5
				// 在H5环境中的处理逻辑
				console.log('当前环境: H5浏览器');
				
				// 在H5环境中，大部分现代浏览器支持HLS
				if (this.videoInfo.hlsUrl) {
					console.log('H5环境使用HLS格式:', this.videoInfo.hlsUrl);
					return this.videoInfo.hlsUrl;
				}
				
				// 部分浏览器通过flv.js支持FLV
				if (this.videoInfo.flvUrl) {
					console.log('H5环境使用FLV格式:', this.videoInfo.flvUrl);
					return this.videoInfo.flvUrl;
				}
				// #endif
				
				// #ifdef APP-PLUS
				// 在APP环境中的处理逻辑
				console.log('当前环境: APP');
				
				// 优先使用HLS格式，在iOS和Android设备上都有良好支持
				if (this.videoInfo.hlsUrl) {
					console.log('APP环境使用HLS格式:', this.videoInfo.hlsUrl);
					return this.videoInfo.hlsUrl;
				}
				
				// 其次考虑FLV格式
				if (this.videoInfo.flvUrl) {
					console.log('APP环境使用FLV格式:', this.videoInfo.flvUrl);
					return this.videoInfo.flvUrl;
				}
				
				// 在某些APP环境中可能支持RTMP
				if (this.videoInfo.rtmpUrl) {
					console.log('APP环境使用RTMP格式:', this.videoInfo.rtmpUrl);
					return this.videoInfo.rtmpUrl;
				}
				// #endif
				
				// #ifdef MP
				// 小程序环境
				console.log('当前环境: 小程序');
				
				// 小程序通常支持HLS格式
				if (this.videoInfo.hlsUrl) {
					console.log('小程序环境使用HLS格式:', this.videoInfo.hlsUrl);
					return this.videoInfo.hlsUrl;
				}
				// #endif
				
				// 默认处理逻辑，所有环境通用
				// 如果以上都不可用，返回原始URL或第一个可用的URL
				if (this.videoInfo.url) {
					console.log('使用默认视频源:', this.videoInfo.url);
					return this.videoInfo.url;
				}
				
				// 最终回退选项，按优先级返回第一个非空URL
				const urls = [this.videoInfo.hlsUrl, this.videoInfo.flvUrl, this.videoInfo.rtmpUrl];
				for (const url of urls) {
					if (url) {
						console.log('使用备选视频源:', url);
						return url;
					}
				}
				
				console.log('无可用视频源');
				return '';
			},
			
			// 开启视频心跳功能
			startHeartbeat() {
				// 先清除可能存在的定时器
				this.stopHeartbeat();
				
				// 如果没有视频ID，不启动心跳
				if (!this.videoId) return;
				
				// 先执行一次心跳
				this.sendHeartbeat();
				
				// 设置定时器，每隔指定时间发送一次心跳
				this.heartbeatTimer = setInterval(() => {
					this.sendHeartbeat();
				}, this.heartbeatInterval * 1000); // 转换为毫秒
			},
			
			// 停止心跳功能
			stopHeartbeat() {
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
					console.log('心跳定时器已清理');
				}
			},
			
			// 发送心跳请求
			sendHeartbeat() {
				this.syghttp.ajax({
					url: this.syghttp.api.liveTransferHeartbeat,
					method: 'GET',
					data: {
						liveId: this.videoId,
						time: this.heartbeatInterval,
						companyId: this.companyId
					},
					success: (res) => {
						console.log('心跳成功:', res);
						// 获取直播间观看人数
						this.getLiveViewerCount();
					},
					fail: (err) => {
						console.error('心跳失败:', err);
					}
				});
			},
			
			// 获取直播间详情
			getLiveDetail() {
				if (!this.videoId) return;
				
				this.syghttp.ajax({
					url: this.syghttp.api.getLiveDetail,
					method: 'POST',
					data: {
						getLiveSize: 0,
						id: this.videoId,
						companyId: this.companyId
					},
					success: (res) => {
						console.log('获取直播间详情成功:', res);
						if (res && res.data && res.data.liveDto) {
							// 更新直播间信息
							const liveData = res.data.liveDto;
							
							// 更新playWay
							this.playWay = liveData.playWay || 0;
							console.log('直播方式:', this.playWay, '是否横屏:', this.playWay === 1);
							
							// 更新视频信息
							this.videoInfo.name = liveData.tittle || this.videoInfo.name;
							this.videoInfo.author = liveData.nickName || this.videoInfo.author;
							this.videoInfo.businessName = liveData.description || this.videoInfo.businessName;
							this.videoInfo.videoThumbnailUrl = liveData.liveCover || this.videoInfo.videoThumbnailUrl;
							
							// 检测方向
							this.checkOrientation();
							
							// 获取直播观看人数
							this.getLiveViewerCount();
						}
					},
					fail: (err) => {
						console.error('获取直播间详情失败:', err);
					}
				});
			},
			
			// 获取直播间观看人数
			getLiveViewerCount() {
				if (!this.videoId) return;
				
				this.syghttp.ajax({
					url: this.syghttp.api.getLiveNumber,
					method: 'GET',
					data: {
						liveId: this.videoId
					},
					success: (res) => {
						console.log('获取直播间观看人数成功:', res);
						// 直接处理数字格式的返回值
						if (res && res.data) {
							// 数据格式：{"code":1000,"msg":"SUCCESS","data":601}
							this.viewerCount = Number(res.data) || 0;
						}
					},
					fail: (err) => {
						console.error('获取直播间观看人数失败:', err);
					}
				});
			},		
			// 格式化数字（1000 -> 1k）
			formatCount(count) {
				if (count < 1000) return count.toString();
				if (count < 10000) return (count / 1000).toFixed(1) + 'k';
				return (count / 10000).toFixed(1) + 'w';
			},
			
			// 格式化视频时长
			formatDuration(seconds) {
				if (!seconds) return '00:00';
				
				const minutes = Math.floor(seconds / 60);
				const remainingSeconds = Math.floor(seconds % 60);
				
				return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
			},
			
			// 播放相关视频
			playRelatedVideo(item) {
				// 关闭推荐抽屉
				this.isRecommendOpen = false;
				// 重置播放结束状态
				this.isPlayEnded = false;
				// 重置播放状态
				this.isInitialLoading = true;
				this.isPlaying = false;
				this.hasShownErrorToast = false;
				
				// 更新视频信息
				this.videoInfo = {
					id: item.id,
					name: item.name,
					url: item.url,
					videoThumbnailUrl: item.videoThumbnailUrl,
					author: item.author || '勃学负责人',
					businessName: item.description
				};
				
				this.videoId = item.id;
				
				// 当前播放视频更新后，需要更新相关视频列表
				if (this.relatedVideos && this.relatedVideos.length > 0) {
					// 过滤出当前视频之外的视频作为相关推荐
					this.relatedVideos = this.relatedVideos.filter(video => video.id !== item.id);
				}
				
				// 如果相关视频列表为空或者数量太少，从API获取更多
				if (!this.relatedVideos || this.relatedVideos.length < 3) {
					// this.getRelatedVideos();
				}
				
				// 延迟关闭加载状态
				setTimeout(() => {
					this.isInitialLoading = false;
					
					// 确保视频播放
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && videoPlayer.play) {
						videoPlayer.play();
					}
				}, 500);
			},
			
			// 视频播放事件
			onVideoPlay() {
				this.isPlaying = true;
				this.isInitialLoading = false;
				this.isPlayEnded = false; // 重置结束状态
				
				// 重置错误计数
				this.streamErrorCount = 0;
				
				// 开始黑屏检测
				this.startStreamCheck();
				
				// 开始假人送礼
				this.startFakeSenders();
				
				// 如果有初始进度但尚未设置，尝试再次设置
				if (!this.initialProgressSet && this.videoInfo.currentProgress > 0) {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && videoPlayer.toSeek) {
						videoPlayer.toSeek(this.videoInfo.currentProgress);
						this.initialProgressSet = true;
					}
				}
			},
			
			// 视频暂停事件
			onVideoPause() {
				this.isPlaying = false;
				// 停止假人送礼
				this.stopFakeSenders();
			},
			
			// 视频播放结束事件
			onVideoEnded() {
				console.log('视频播放结束');
				// 停止假人送礼
				this.stopFakeSenders();
				// 显示播放结束提示
				this.showPlayEndedTips();
			},
			
			// 显示播放结束提示
			showPlayEndedTips(message) {
				// 停止检测
				this.stopStreamCheck();
				
				// 显示结束提示
				this.isPlayEnded = true;
				this.isPlaying = false;
				
				// 如果提供了自定义消息，更新UI上的提示文字
				if (message) {
					// 更新标题文本数据属性
					this.endTitleText = message;
					
					// 使用$nextTick确保DOM已更新
					this.$nextTick(() => {
						// 如果是错误状态，更新按钮
						if (this.$refs.endBtnRef && (this.isLiveError || message.includes('异常') || message.includes('刷新'))) {
							// 使用Vue API更新按钮内容
							this.$refs.endBtnRef.innerHTML = `
							  <u-icon name="reload" color="#FFFFFF" size="18"></u-icon>
							  <text>刷新重试</text>
							`;
						
							// 修改按钮点击事件
							this.$refs.endBtnRef.onclick = () => {
								// 刷新当前页面
								location.reload();
							};
						}
					});
				}
				
				// 尝试停止视频播放
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer) {
					videoPlayer.pause();
				}
				
				// 停止心跳
				if (this.heartbeatTimer) {
					this.stopHeartbeat();
					console.log('已停止心跳');
				}
				
				// 清除所有定时器
				if (this.liveStatusCheckTimer) {
					clearTimeout(this.liveStatusCheckTimer);
					this.liveStatusCheckTimer = null;
				}
			},
			
			// 视频播放错误事件
			onVideoError(e) {
				console.error('Video Error:', e);
				
				// 增加错误计数
				this.streamErrorCount++;
				
				// 如果错误次数超过阈值，认为直播已经结束
				if (this.streamErrorCount >= this.maxStreamErrors) {
					console.log('检测到多次视频错误，判断为直播已结束');
					this.showPlayEndedTips();
					return;
				}
				
				// 检查错误信息是否包含直播结束的特征
				if (e && typeof e === 'object') {
					// 检查网络错误和解码错误 (code 2和3通常与流结束有关)
					if (e.code === 2 || e.code === 3) {
						console.log('检测到视频网络或解码错误，可能是直播已结束');
						this.showPlayEndedTips();
						return;
					}
					
					// 专门处理安卓设备上的错误码4 (各种格式错误)
					if (e.code === 4 && e.message) {
						// 荣耀手机特殊处理 - DEMUXER_ERROR_COULD_NOT_PARSE
						if (e.message.includes('DEMUXER_ERROR_COULD_NOT_PARSE')) {
							console.log('检测到荣耀手机特有的解码错误，直接显示提示');
							// 立即显示直播异常，不尝试重新加载
							this.showPlayEndedTips('直播加载异常，请尝试刷新');
							// 标记为错误状态，提供刷新按钮
							this.isLiveError = true;
							return;
						}
						
						// 其他格式错误处理
						if (e.message.includes('MEDIA_ELEMENT_ERROR: Format error') || 
							e.message.includes('PIPELINE_ERROR_EXTERNAL_RENDERER_FAILED')
						) {
							console.log('检测到安卓特定渲染错误，可能是直播异常');
							
							// 不再尝试重新加载视频，直接提示直播异常
							// 因为重新加载通常会导致 NotSupportedError 错误
							const videoPlayer = this.$refs.domVideoPlayer;
							// 安卓上遇到格式错误，通常是直播流异常，直接显示提示
							this.showPlayEndedTips('直播流异常，请稍后再试');
							
							/* 不再尝试重新加载，因为会导致额外错误
							if (false && videoPlayer && this.streamErrorCount <= 1) {
								console.log('尝试重新加载视频...');
								// 延迟500ms后重试
								setTimeout(() => {
									if (videoPlayer.play) {
										videoPlayer.play();
									}
								}, 500);
								return;
							} */
							
							return;
						}
					}
					
					// 检查错误信息中的关键字
					const errorMsg = e.message || '';
					if (errorMsg.toLowerCase().includes('end of stream') || 
						errorMsg.toLowerCase().includes('network error') || 
						errorMsg.toLowerCase().includes('aborted') || 
						errorMsg.toLowerCase().includes('no data')) {
						console.log('检测到流结束相关错误信息');
						this.showPlayEndedTips();
						return;
					}
				} else if (typeof e === 'string' && 
					(e.includes('MediaSource onSourceEnded') || 
					e.includes('NetworkError') || 
					e.includes('AbortError'))) {
					console.log('检测到直播已结束');
					this.showPlayEndedTips();
					return;
				}
				
				// 如果视频实际正在播放，就不弹出错误提示
				if (this.isPlaying) return;
				
				// 避免多次显示错误提示
				if (!this.hasShownErrorToast) {
					this.hasShownErrorToast = true;
					this.isInitialLoading = false;
					
					// 尝试重新播放视频
					setTimeout(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && videoPlayer.play) {
							videoPlayer.play();
						}
					}, 1000);
				}
			},
			
			// 添加直播流结束事件处理方法
			onSourceEnded(reason) {
				console.log('直播源结束事件:', reason);
				
				// 检查是在开播后发生的错误，还是一开始就报错
				const isInitialError = !this.isPlaying || this.streamErrorCount === 1;
				
				// 根据原因决定显示什么提示
				if (reason === 'android_format_error' || 
				   reason === 'android_renderer_failed' || 
				   (reason && (reason.includes('Format error') || 
				              reason.includes('PIPELINE_ERROR')))) {
					// 对安卓格式错误提供更友好的提示信息
					if (isInitialError) {
						// 一开始就发生的错误
						this.isLiveError = true;
						this.showPlayEndedTips('直播加载失败，请稍后再试');
					} else {
						// 在播放过程中发生的错误，检查直播状态
						this.checkLiveStatus('直播已结束');
					}
				} else {
					// 其他类型的错误，检查直播状态确认是否真的结束
					this.checkLiveStatus('直播已结束');
				}
			},
			
			// 添加检查直播状态的方法
			checkLiveStatus(defaultMessage) {
				// 清除可能存在的定时器
				if (this.liveStatusCheckTimer) {
					clearTimeout(this.liveStatusCheckTimer);
					this.liveStatusCheckTimer = null;
				}
				
				// 调用API检查直播状态
				this.syghttp.ajax({
					url: this.syghttp.api.getIndexLiving,
					method: 'POST',
					data: {
						"page": {
							"maxResultCount": 0,
							"pageNo": 1,
							"pageSize": 10,
							"skipCount": 0
						},
						"companyId": this.companyId
					},
					success: (res) => {
						console.log('检查直播状态:', res);
						
						// 默认假设直播已结束
						let isLiveActive = false;
						let message = defaultMessage || '直播已结束';
						
						// 处理返回数据
						if (res.code === 1000 && res.data) {
							// 检查是否有直播数据
							let liveItems = [];
							
							// 检查liveDtos列表
							if (res.data.liveDtos && Array.isArray(res.data.liveDtos)) {
								liveItems = liveItems.concat(res.data.liveDtos);
							}
							
							// 检查items列表
							if (res.data.items && res.data.items.items && Array.isArray(res.data.items.items)) {
								liveItems = liveItems.concat(res.data.items.items);
							}
							
							if (liveItems.length === 0) {
								console.log('没有直播间数据，直播可能已结束');
								message = '直播已结束';
							} else {
								// 查找当前直播ID
								const currentLive = liveItems.find(item => item.id === this.videoId);
								
								if (!currentLive) {
									console.log('当前直播间不在列表中，直播可能已结束');
									message = '直播已结束';
								} else {
									// 检查直播状态
									if (currentLive.identification === 1) {
										console.log('直播仍在进行中，可能是临时网络问题');
										isLiveActive = true;
										message = '直播连接中断，请稍后再试';
									} else {
										console.log('直播间存在但未开播，直播可能已结束');
										message = '直播已结束';
									}
								}
							}
						}
						
						if (isLiveActive) {
							// 如果直播仍在进行中，设置错误提示
							this.isLiveError = true;
							this.isLiveEnded = false;
							message = '直播连接中断，请稍后再试'; // 确保消息被正确设置
							console.log('设置直播连接中断提示:', message);
						} else {
							// 直播已确认结束
							this.isLiveError = false;
							this.isLiveEnded = true;
							message = '直播已结束';
							console.log('设置直播结束提示:', message);
						}
						
						// 显示对应提示
						this.showPlayEndedTips(message);
					},
					fail: (err) => {
						console.error('检查直播状态失败:', err);
						// 接口调用失败，使用默认提示
						this.showPlayEndedTips(defaultMessage);
					}
				});
			},
			
			// 开始检测直播流状态
			startStreamCheck() {
				// 清除可能存在的定时器
				this.stopStreamCheck();
				
				// 初始化检测变量
				this.lastPlaybackTime = 0;
				this.stuckFrameCounter = 0;
				
				// 设置定时器每秒检查
				this.streamCheckTimer = setInterval(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (!videoPlayer) return;
					
					// 获取当前播放时间
					const currentTime = videoPlayer.currentTime || 0;
					
					// 如果视频在播放但时间没有变化
					if (this.isPlaying && currentTime > 0 && currentTime === this.lastPlaybackTime) {
						this.stuckFrameCounter++;
							console.log('检测到视频卡住，计数:', this.stuckFrameCounter);
						
						// 如果连续多次卡住(5秒)，可能是直播已结束
						if (this.stuckFrameCounter >= 15) {
							console.log('检测到视频卡住超过15秒，判断为直播已结束');
									this.showPlayEndedTips();
						}
					} else if (this.isPlaying) {
						// 重置计数器
						this.stuckFrameCounter = 0;
						this.lastPlaybackTime = currentTime;
					}
				}, 1000);
			},
			
			// 停止检测直播流状态
			stopStreamCheck() {
				if (this.streamCheckTimer) {
					clearInterval(this.streamCheckTimer);
					this.streamCheckTimer = null;
				}
			},
			
			// 切换播放/暂停
			togglePlay() {
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer) {
					if (this.isPlaying) {
						videoPlayer.pause();
					} else {
						videoPlayer.play();
					}
				}
			},
			
			// 返回上一页
			goBack() {
				// 完全清理所有资源
				this.clearAllTimers();
				
				// 返回上一页
				uni.navigateBack();
			},
			
			// 切换推荐抽屉
			toggleRecommend() {
				this.isRecommendOpen = !this.isRecommendOpen;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 切换点赞状态
			toggleLike() {
				this.isLiked = !this.isLiked;
				if (this.isLiked) {
					this.likeCount++;
					// 显示点赞动画 - 延长显示时间，确保GIF加载完成
					this.showLikeAnimation = true;
					setTimeout(() => {
						this.showLikeAnimation = false;
					}, 4000); // 延长到4秒，确保GIF加载和播放完整
				} else {
					this.likeCount--;
				}
				// 确保视频继续播放
				if (!this.isPlaying && !this.isPlayEnded) {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer) {
						videoPlayer.play();
					}
				}
			},
			
			// 切换关注状态
			toggleFollow() {
				this.isFollowed = !this.isFollowed;
				uni.showToast({
					title: this.isFollowed ? '关注成功' : '已取消关注',
					icon: 'none'
				});
				// 确保视频继续播放
				if (!this.isPlaying && !this.isPlayEnded) {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer) {
						videoPlayer.play();
					}
				}
			},
			
			// 显示评论
			showComments() {
				this.showCommentPopup = true;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 关闭评论
			closeComments() {
				this.showCommentPopup = false;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 点赞评论
			likeComment(index) {
				if (!this.commentList[index]) return;
				
				this.commentList[index].isLiked = !this.commentList[index].isLiked;
				if (this.commentList[index].isLiked) {
					this.commentList[index].likes++;
				} else {
					this.commentList[index].likes--;
				}
			},
			
			// 发送评论
			sendComment() {
				if (!this.commentText.trim()) return;
				
				// 添加新评论到列表
				this.commentList.unshift({
					userName: '我', // 用户名
					content: this.commentText,
					time: '刚刚',
					likes: 0,
					isLiked: false
				});
				
				// 增加评论计数
				this.commentCount++;
				
				// 清空输入框
				this.commentText = '';
				
				// 提示成功
				uni.showToast({
					title: '评论成功',
					icon: 'none'
				});
			},
			
			// 分享视频
			shareVideo() {
				// #ifdef H5
				this.showSharePopup = true;
				// #endif
				
				// #ifdef APP-PLUS
				uniShare.show({
					content: { //公共的分享参数配置
						type: 0,
						href: 'https://bxcs.boxuehao.cn/app/',
						// href: this.currentUrl,
						title: this.videoInfo.name || '精彩直播内容',
						summary: `来自${this.videoInfo.author || '勃学负责人'}的精彩直播`,
						imageUrl:'/static/app-plus/sharemenu/logo.png'
					},
					menus: [{
							"img": "/static/app-plus/sharemenu/wechatfriend.png",
							"text": "微信好友",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneSession"
							}
						},
						{
							"img": "/static/app-plus/sharemenu/hp.png",
							"text": "海报",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneSession",
								"type": 2,
								"imageUrl": this.poserPath || 'https://bxcs.boxuehao.cn/bxcs/static/image/erweima.png'
							}
						},
						{
							"img": "/static/app-plus/sharemenu/wechatmoments.png",
							"text": "微信朋友圈",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneTimeline"
							}
						},
						{
							"img": "/static/app-plus/sharemenu/copyurl.png",
							"text": "复制",
							"share": "copyurl"
						},
						{
							"img": "/static/app-plus/sharemenu/more.png",
							"text": "更多",
							"share": "shareSystem"
						}
					],
					cancelText: "取消分享",
				}, e => {
					console.log(e);
					// 确保视频继续播放
					setTimeout(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
							videoPlayer.play();
						}
					}, 50);
				});
				// #endif
			},
			
			// 获取当前页面完整URL
			getCurrentPageUrl() {
				// #ifdef H5
				this.currentUrl = window.location.href;
				// #endif
				
				// #ifdef APP-PLUS
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const route = currentPage.route;
				const options = currentPage.options;
				
				// 构建查询字符串
				let queryString = '';
				if (Object.keys(options).length > 0) {
					queryString = '?' + Object.keys(options)
						.map(key => `${key}=${options[key]}`)
						.join('&');
				}
				
				// 获取当前域名
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					const domain = widgetInfo.origin || this.http.poserUrl;
					this.currentUrl = `${domain}/${route}${queryString}`;
				});
				// #endif
			},
			
			// 复制链接 (H5)
			copyLink() {
				// #ifdef H5
				uni.setClipboardData({
					data: window.location.href,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success'
						});
						this.showSharePopup = false;
					}
				});
				// #endif
			},
			
			// 关闭H5分享弹窗
			closeSharePopup() {
				this.showSharePopup = false;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 重播视频
			replayVideo() {
				this.isPlayEnded = false;
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.toSeek) {
					videoPlayer.toSeek(0);
					setTimeout(() => {
						videoPlayer.play();
					}, 100);
				}
			},
			
			// 播放下一个视频
			playNextVideo() {
				if (this.relatedVideos && this.relatedVideos.length > 0) {
					// 随机选择一个视频播放，增加用户体验多样性
					const randomIndex = Math.floor(Math.random() * this.relatedVideos.length);
					const nextVideo = this.relatedVideos[randomIndex];
					
					// 播放选中的视频
					this.playRelatedVideo(nextVideo);
					
					// 从推荐列表中移除已播放的视频
					this.relatedVideos = this.relatedVideos.filter((_, index) => index !== randomIndex);
				} else {
					// 如果没有推荐视频，尝试重新获取
					// this.getRelatedVideos();
					uni.showToast({
						title: '正在获取更多视频...',
						icon: 'none'
					});
				}
			},
			
			// 窗口大小变化回调
			windowResizeCallback(res) {
				// 记录窗口尺寸
				this.windowWidth = res.size.windowWidth;
				this.windowHeight = res.size.windowHeight;
				
				// 检测横竖屏
				this.checkOrientation();
			},
			
			// 检测屏幕方向
			checkOrientation() {
				// 使用 playWay 参数判断视频是横屏还是竖屏
				// playWay=1 表示横屏，playWay=0 表示竖屏
				const isLandscapeVideo = this.playWay === 1;
				
				// 获取当前设备方向
				const info = uni.getSystemInfoSync();
				const isDeviceLandscape = info.windowWidth > info.windowHeight;
				
				// 如果是横屏视频，但设备是竖屏，显示旋转提示
				if (isLandscapeVideo && !isDeviceLandscape) {
					this.showRotationHint();
				}
				
				// 如果横屏状态发生变化（这里指的是设备方向变化）
				if (this.isLandscape !== isDeviceLandscape) {
					this.isLandscape = isDeviceLandscape;
					
					if (this.isLandscape) {
						// 横屏模式
						this.isControlsVisible = true;
						// 横屏模式下，5秒后自动隐藏控制栏
						this.startAutoHideControls();
						
						// 不强制锁定屏幕方向，让用户自由旋转
						// #ifdef APP-PLUS
						plus.screen.unlockOrientation();
						// #endif
					} else {
						// 竖屏模式
						this.isControlsVisible = true;
						// 竖屏模式下，清除自动隐藏计时器
						this.clearAutoHideControls();
						
						// 如果是横屏视频，但设备回到竖屏，再次显示旋转提示
						if (isLandscapeVideo) {
							this.showRotationHint();
						}
						
						// 不强制锁定竖屏
						// #ifdef APP-PLUS
						plus.screen.unlockOrientation();
						// #endif
					}
					
					// 确保视频继续播放
					this.$nextTick(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
							videoPlayer.play();
						}
					});
				}
			},
			
			// 开始自动隐藏控制栏的计时器 - 同时应用于竖屏和横屏模式
			startAutoHideControls() {
				this.clearAutoHideControls();
				this.isControlsVisible = true;
				this.controlsTimer = setTimeout(() => {
					this.isControlsVisible = false;
				}, 5000); // 5秒后自动隐藏
			},
			
			// 清除自动隐藏控制栏的计时器
			clearAutoHideControls() {
				if (this.controlsTimer) {
					clearTimeout(this.controlsTimer);
					this.controlsTimer = null;
					console.log('控制栏隐藏定时器已清理');
				}
				
				if (this.landscapeTimer) {
					clearTimeout(this.landscapeTimer);
					this.landscapeTimer = null;
					console.log('横屏控制栏定时器已清理');
				}
			},
			
			// 触摸屏幕时显示控制栏
			showControlsOnTouch() {
				// 如果礼物面板打开，则不触发控制栏显示
				if (this.showGiftPopup) return;
				
				this.isControlsVisible = true;
				this.startAutoHideControls(); // 重新开始计时
			},
			
			// 点击视频区域时切换控制栏显示状态
			toggleControlsVisible() {
				// 如果推荐列表抽屉打开，则不触发控制栏显示/隐藏
				if (this.isRecommendOpen) return;
				
				// 如果礼物面板打开，则不触发控制栏显示/隐藏
				if (this.showGiftPopup) return;
				
				this.isControlsVisible = !this.isControlsVisible;
				if (this.isControlsVisible) {
					this.startAutoHideControls();
				}
			},
			
			// 显示旋转提示
			showRotationHint() {
				// 只有在横屏视频(playWay=1)时才显示旋转提示
				if (this.playWay !== 1) return;
				
				// 显示旋转提示
				this.showRotateHint = true;
				
				// 4秒后自动隐藏
				setTimeout(() => {
					this.showRotateHint = false;
				}, 4000);
			},
			
			// 添加一个统一的资源清理方法
			clearAllTimers() {
				// 停止心跳
				this.stopHeartbeat();
				
				// 清除控制栏自动隐藏定时器
				this.clearAutoHideControls();
				
				// 停止流检测
				this.stopStreamCheck();
				
				// 停止假人送礼
				this.stopFakeSenders();
				
				// 确保没有遗漏的定时器
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
				}
				
				if (this.controlsTimer) {
					clearTimeout(this.controlsTimer);
					this.controlsTimer = null;
				}
				
				if (this.landscapeTimer) {
					clearTimeout(this.landscapeTimer);
					this.landscapeTimer = null;
				}
				
				// 清除直播状态检查定时器
				if (this.liveStatusCheckTimer) {
					clearTimeout(this.liveStatusCheckTimer);
					this.liveStatusCheckTimer = null;
				}
				
				console.log('所有定时器已清理');
			},
			
			// 添加暂停所有资源的方法
			pauseAllResources() {
				console.log('暂停所有资源 - 直播模式下保持播放');
				
				// 在直播模式下，不暂停视频播放
				// 只暂停心跳，但不清除定时器对象
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					// 不设置为null，保留定时器引用以便恢复
				}
				
				// 暂停控制栏自动隐藏
				this.clearAutoHideControls();
			},
			
			// 添加恢复所有资源的方法
			resumeAllResources() {
				console.log('恢复所有资源');
				
				// 恢复视频播放
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.play) {
					videoPlayer.play();
					this.isPlaying = true;
				}
				
				// 恢复心跳
				this.startHeartbeat();
				
				// 恢复控制栏自动隐藏
				this.startAutoHideControls();
			},
			
			// 显示礼物面板
			showGiftPanel() {
				// 如果没有礼物列表数据，先加载
				if (this.giftList.length === 0) {
					this.loadGiftList();
				}
				this.showGiftPopup = true;
			},
			
			// 加载礼物列表
			loadGiftList() {
				try {
					// 使用图片列表替代gift.json
					const giftList = [
						// { id: '1', name: '城堡', url: 'https://bxcs.boxuehao.cn/bxcs/chengbao.png' },
						{ id: '2', name: '鲜花', url: 'https://bxcs.boxuehao.cn/bxcs/hua.png' },
						{ id: '3', name: '嘉年华', url: 'https://bxcs.boxuehao.cn/bxcs/jianianhua.png' },
						// { id: '4', name: '鲸鱼', url: 'https://bxcs.boxuehao.cn/bxcs/jingyu.png' },
						// { id: '5', name: '龙', url: 'https://bxcs.boxuehao.cn/bxcs/long.png' },
						{ id: '6', name: '玫瑰', url: 'https://bxcs.boxuehao.cn/bxcs/meigui.png' },
						{ id: '7', name: '啤酒', url: 'https://bxcs.boxuehao.cn/bxcs/pijiu.png' },
						{ id: '8', name: '人气票', url: 'https://bxcs.boxuehao.cn/bxcs/renqipiao.png' },
						{ id: '9', name: '棒棒糖', url: 'https://bxcs.boxuehao.cn/bxcs/bangbangtang.png' },
						{ id: '10', name: '爱心', url: 'https://bxcs.boxuehao.cn/bxcs/xiaoxinxin.png' },
						{ id: '11', name: '真好看', url: 'https://bxcs.boxuehao.cn/bxcs/zhenhaokan.png' }
					];
					
					this.giftList = giftList;
				} catch (e) {
					console.error('加载礼物列表失败:', e);
				}
			},
			
			// 选择礼物
			selectGift(gift) {
				this.selectedGift = gift;
			},
			
			// 发送礼物
			sendGift() {
				if (!this.selectedGift) return;
				
				// 区分横竖屏处理
				if (this.isLandscape) {
					// 横屏模式 - 添加弹幕动画
					this.addGiftDanmaku({
						id: Date.now(),
						sender: '我',
						gift: this.selectedGift,
						avatar: '/static/image/default-avatar.png' // 可以替换为用户自己的头像
					});
				} else {
					// 竖屏模式 - 播放视频动画
					this.playGiftVideo();
				}
				
				// 关闭礼物面板
				this.showGiftPopup = false;
				
				// 重置选中礼物
				this.selectedGift = null;
			},
			
			// 播放竖屏礼物视频 - 使用页面跳转
			playGiftVideo() {
				if (!this.selectedGift) return;
				
				// 标记为从礼物动画页面返回
				this.isFromGiftAnimation = true;
				
				// 根据选中的礼物找到对应的Lottie JSON文件
				const giftName = this.selectedGift.url.split('/').pop().replace('.png', '');
				const lottieUrl = `https://bxcs.boxuehao.cn/bxcs/${giftName}.json`;
				
				// 保存当前直播状态，供恢复使用
				this.saveLiveState();
				
				// 使用独立页面播放动画
				uni.navigateTo({
					url: `/pages/investment/lottie?url=${encodeURIComponent(lottieUrl)}`,
				});
			},
			
			// 保存直播状态
			saveLiveState() {
				// 保存所有需要在返回后恢复的状态
				this.liveStateData = {
					videoId: this.videoId,
					videoUrl: this.videoUrl,
					isPlaying: this.isPlaying,
					// 添加其他需要保存的状态...
				};
				console.log('保存直播状态:', this.liveStateData);
			},
			
			// 恢复直播状态
			restoreLiveState() {
				if (!this.liveStateData) return;
				
				console.log('恢复直播状态:', this.liveStateData);
				// 恢复播放状态
				if (this.liveStateData.isPlaying) {
					// 确保视频继续播放
					setTimeout(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && videoPlayer.play) {
							videoPlayer.play();
							this.isPlaying = true;
						}
					}, 300);
				}
				
				// 重置状态数据，避免重复恢复
				this.liveStateData = null;
			},
			
			// 礼物动画完成事件处理
			onGiftAnimationComplete() {
				console.log('礼物动画播放完成');
				// 隐藏Lottie动画
				this.showGiftAnimation = false;
			},
			
			// 礼物动画错误事件处理
			onGiftAnimationError() {
				console.error('礼物动画加载失败');
				this.showGiftAnimation = false;
			},
			
			// 添加横屏弹幕礼物
			addGiftDanmaku(danmaku) {
				// 最多同时显示5个弹幕
				if (this.giftDanmakus.length >= 5) {
					this.giftDanmakus.shift(); // 移除最早的一个
				}
				
				// 添加新弹幕
				this.giftDanmakus.push(danmaku);
				
				// 10秒后自动移除
				setTimeout(() => {
					const index = this.giftDanmakus.findIndex(item => item.id === danmaku.id);
					if (index !== -1) {
						this.giftDanmakus.splice(index, 1);
					}
				}, 7000);
			},
			
			// 开始假人送礼
			startFakeSenders() {
				// 先清除可能存在的定时器
				this.stopFakeSenders();
				
				// 设置定时器，随机时间发送礼物
				this.fakeSenderTimer = setInterval(() => {
					// 只有在直播正在播放且有观众时才显示假人送礼
					if (this.isPlaying && this.viewerCount > 0) {
						// 70%的几率触发
						if (Math.random() > 0.1) {
							this.sendFakeGift();
						}
					}
				}, 15000 + Math.random() * 20000); // 15-35秒随机间隔
				
				// 立即发送一个
				setTimeout(() => {
					if (this.isPlaying) {
						this.sendFakeGift();
					}
				}, 5000);
			},
			
			// 停止假人送礼
			stopFakeSenders() {
				if (this.fakeSenderTimer) {
					clearInterval(this.fakeSenderTimer);
					this.fakeSenderTimer = null;
				}
			},
			
			// 发送假人礼物
			sendFakeGift() {
				// 确保有礼物列表
				if (this.giftList.length === 0) {
					this.loadGiftList();
					return;
				}
				
				// 随机选择一个礼物
				const randomGift = this.giftList[Math.floor(Math.random() * this.giftList.length)];
				
				// 随机选择一个名字
				const randomName = this.fakeNames[Math.floor(Math.random() * this.fakeNames.length)];
				
				// 如果是横屏，添加弹幕
				if (this.isLandscape) {
					this.addGiftDanmaku({
						id: Date.now(),
						sender: randomName,
						gift: randomGift
					});
				}
			},
			
			// 开始礼物动画
			startGiftAnimation() {
				if (this.isShowingGift || this.giftAnimations.length === 0) return;
				
				this.isShowingGift = true;
				const giftAnimation = this.giftAnimations[0];
				
				// 3秒后移除礼物动画
				setTimeout(() => {
					this.giftAnimations.shift();
					this.isShowingGift = false;
					this.startGiftAnimation(); // 继续下一个礼物动画
				}, 3000);
			},
			
			// 关闭礼物面板
			closeGiftPanel() {
				this.showGiftPopup = false;
				// 重置选中礼物
				this.selectedGift = null;
				
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 300); // 等待动画完成后再检查播放状态
			},
		}
	}
</script>

<style lang="scss" scoped>
	.live-room-container {
		width: 100%;
		// height: 100vh;
		position: relative;
		background-color: #000;
		overflow: hidden;
	}
	
	/* 全屏视频播放器 - 修改为覆盖整个屏幕 */
	.full-video-player {
		width: 100%;
		height: 100vh;
		z-index: 1;
		background-color: #000; 
		transition: all 0.3s ease;
		object-fit: cover; /* 确保视频覆盖整个区域 */
	}
	
	.landscape-video {
		object-position: center;
	}
	
	/* 横屏模式样式调整 */
	@media screen and (orientation: landscape) {
		.full-video-player {
			width: 100vw;
			height: 100vh;
		}
		
		.top-controls {
			padding: 20rpx;
			height: 60rpx;
		}
		
		.live-info {
			margin-top: 0;
			height: 60rpx;
			align-items: center;
		}
		
		.right-controls {
			display: none; /* 横屏模式下隐藏右侧控制栏 */
		}
		
		.live-author-info {
			max-width: 60vw;
		}
		
		.comment-container {
			max-height: 90vh;
		}
		
		.comment-list {
			max-height: 70vh;
		}
		
		.play-btn-center {
			transform: translate(-50%, -50%) scale(0.8);
		}
		
		.recommend-drawer {
			display: none; /* 横屏模式下隐藏推荐抽屉 */
		}
		
		.play-end-tips {
			.play-end-content {
				width: 50%;
			}
		}
	}
	
	/* 顶部控制栏 */
	.top-controls {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		padding: calc(var(--status-bar-height) + 30rpx) 20rpx 20rpx; /* 增加顶部边距 */
		z-index: 10;
		transition: all 0.3s ease;
	}
	
	.landscape-top-controls {
		padding: calc(var(--status-bar-height) ) 40rpx 30rpx 40rpx !important;
		background: linear-gradient(to bottom, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.4) 80%, transparent 100%);
		height: auto;
		width: 100%;
		display: flex;
		align-items: center;
		z-index: 1000;
	}
	
	.controls-hidden {
		opacity: 0;
		transform: translateY(-100%);
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-image: linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0));
		z-index: -1;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		margin-right: 15rpx;
	}
	
	.live-info {
		margin-top: 40rpx; /* 增加顶部边距 */
		display: flex;
		align-items: center;
		transition: all 0.3s ease;
	}
	
	.live-author-info {
		flex: 1;
		margin-left: 10rpx;
		transition: all 0.3s ease;
	}
	
	.landscape-author-info {
		margin-left: 20rpx;
		max-width: 80%;
	}
	
	.live-author-name {
		font-size: 28rpx;
		color: #FFFFFF;
		font-weight: bold;
		margin-bottom: 6rpx;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
	}
	
	.live-title {
		font-size: 22rpx;
		color: rgba(255,255,255,0.8);
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		display: flex;
		align-items: center;
	}
	
	.viewer-count {
		padding: 2rpx 10rpx;
		background-color: rgba(0, 0, 0, 0.4);
		color: #FFFFFF;
		font-size: 20rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		margin-left: 12rpx;
		height: 30rpx;
	}
	
	.viewer-count text {
		margin-left: 4rpx;
		font-size: 20rpx;
	}
	
	.follow-btn {
		padding: 8rpx 24rpx;
		background-color: #FF5F5F;
		color: #FFFFFF;
		font-size: 22rpx;
		border-radius: 30rpx;
		font-weight: bold;
	}
	
	/* 右侧控制栏 - 添加隐藏动画 */
	.right-controls {
		position: absolute;
		right: 20rpx;
		bottom: 300rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		z-index: 10;
		transition: all 0.3s ease;
		
		&.controls-hidden {
			opacity: 0;
			transform: translateX(100%);
		}
	}
	
	.control-icon-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
		
		text {
			margin-top: 6rpx;
			font-size: 22rpx;
			color: #FFFFFF;
			text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
		}
		
		.icon-wrapper {
			width: 70rpx;
			height: 70rpx;
			border-radius: 50%;
			background-color: rgba(0, 0, 0, 0.3);
			display: flex;
			justify-content: center;
			align-items: center;
			
			&.active {
				background-color: rgba(255, 95, 95, 0.2);
				color: #FF5F5F;
			}
		}
	}
	
	/* 播放按钮 */
	.play-btn-center {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 10;
		
		.play-btn-icon {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
			background-color: rgba(0, 0, 0, 0.5);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	
	/* 推荐视频抽屉样式修改 */
	.recommend-drawer {
		position: absolute;
		right: -80vw;
		top: 0;
		width: 80vw;
		height: 100vh;
		background-color: #121212;
		z-index: 30;
		transition: transform 0.3s ease-out;
		display: flex;
		flex-direction: column;
	}
	
	.drawer-open {
		transform: translateX(-100%);
	}
	
	.drawer-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #333333;
	}
	
	.drawer-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
	}
	
	.drawer-close {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.recommend-list {
		flex: 1;
		padding: 0 30rpx;
	}
	
	.recommend-item {
		display: flex;
		flex-direction: column;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #333333;
		position: relative;
	}
	
	.recommend-cover-container {
		width: 100%;
		height: 204rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 12rpx;
	}
	
	.recommend-cover {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.recommend-info {
		width: 100%;
	}
	
	.recommend-name {
		font-size: 28rpx;
		color: #FFFFFF;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.recommend-meta {
		display: flex;
		justify-content: space-between;
		font-size: 22rpx;
		color: #999999;
	}
	
	/* 评论弹窗 */
	.comment-container {
		background-color: #121212;
		border-radius: 30rpx 30rpx 0 0;
		max-height: 70vh;
		display: flex;
		flex-direction: column;
	}
	
	.comment-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #333333;
	}
	
	.comment-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
	}
	
	.comment-close {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.comment-list {
		flex: 1;
		max-height: 50vh;
		padding: 0 30rpx;
	}
	
	.comment-item {
		display: flex;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #333333;
	}
	
	.comment-content {
		flex: 1;
		overflow: hidden;
	}
	
	.comment-user {
		font-size: 26rpx;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 8rpx;
	}
	
	.comment-text {
		font-size: 26rpx;
		color: #CCCCCC;
		line-height: 1.4;
		margin-bottom: 8rpx;
	}
	
	.comment-time {
		font-size: 22rpx;
		color: #666666;
	}
	
	.comment-like {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-left: 30rpx;
		
		text {
			font-size: 22rpx;
			color: #666666;
			margin-top: 6rpx;
			
			&.liked {
				color: #FF5F5F;
			}
		}
	}
	
	.comment-input-area {
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		border-top: 1rpx solid #333333;
	}
	
	.comment-input-box {
		flex: 1;
		background-color: #222222;
		border-radius: 40rpx;
		padding: 15rpx 30rpx;
		
		input {
			width: 100%;
			height: 60rpx;
			font-size: 26rpx;
			color: #FFFFFF;
		}
	}
	
	.comment-send-btn {
		width: 120rpx;
		height: 60rpx;
		margin-left: 20rpx;
		background-color: #333333;
		border-radius: 30rpx;
		color: #CCCCCC;
		font-size: 26rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		
		&.active {
			background-color: #00C1CC;
			color: #FFFFFF;
		}
	}
	
	/* 空状态 */
	.empty-recommend, .empty-comment {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 100rpx 0;
	}
	
	.empty-text {
		font-size: 26rpx;
		color: #666666;
		margin-top: 20rpx;
	}
	
	/* 加载层 */
	.loading-layer {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba(0,0,0,1);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 40;
	}
	
	/* 播放结束提示 - 改进UI设计 */
	.play-end-tips {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba(0,0,0,0.8);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 30;
	}
	
	.play-end-content {
		background-color: rgba(25,25,25,0.95);
		border-radius: 20rpx;
		padding: 40rpx;
		width: 60%;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.5);
	}
	
	.play-end-title {
		font-size: 28rpx;
		color: #FFFFFF;
		margin-bottom: 30rpx;
		text-align: center;
		font-weight: bold;
	}
	
	.play-end-thumbnail {
		width: 100%;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
		position: relative;
		
		image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		
		.play-end-thumbnail-mask {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0,0,0,0.4);
		}
	}
	
	.play-end-btns {
		display: flex;
		justify-content: space-around;
		width: 100%;
	}
	
	.play-end-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 16rpx 20rpx;
		border-radius: 10rpx;
		margin: 0 10rpx;
		
		text {
			margin-top: 8rpx;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}
	
	.replay {
		background-color: rgba(50,50,50,0.9);
	}
	
	.next {
		background-color: rgba(0,193,204,0.9);
	}
	
	.back {
		background-color: rgba(212,48,48,0.9);
	}
	
	.share-popup {
		padding: 30rpx;
		background-color: #121212;
	}
	
	.share-title {
		text-align: center;
		font-size: 28rpx;
		color: #CCCCCC;
		margin-bottom: 30rpx;
	}
	
	.share-options {
		display: flex;
		justify-content: space-around;
		padding: 20rpx 0 40rpx;
	}
	
	.share-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.share-item image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 16rpx;
	}
	
	.share-item text {
		font-size: 24rpx;
		color: #FFFFFF;
	}
	
	.share-cancel {
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 28rpx;
		color: #FFFFFF;
		border-top: 1rpx solid #333333;
	}
	
	.share-cancel:active {
		background-color: #333333;
	}
	
	.landscape-info {
		margin-top: 0;
		display: flex;
		align-items: center;
		width: 100%;
	}
	
	.landscape-author-info {
		margin-left: 20rpx;
		max-width: 80%;
	}
	
	.landscape-author-info .live-author-name {
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}
	
	.landscape-author-info .live-title {
		font-size: 24rpx;
		max-width: 70vw;
	}
	
	.landscape-top-controls {
		padding: 30rpx 40rpx;
		background: linear-gradient(to bottom, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.4) 80%, transparent 100%);
		height: auto;
		width: 100%;
		display: flex;
		align-items: center;
	}
	
	/* 横屏提示动画 */
	.rotate-toast {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 9999;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 24rpx;
		padding: 40rpx;
		width: 300rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.5);
		animation: fadeInOut 4s ease-in-out;
	}
	
	.rotate-text {
		color: white;
		font-size: 28rpx;
		margin-top: 30rpx;
		text-align: center;
	}
	
	@keyframes fadeInOut {
		0% { opacity: 0; }
		20% { opacity: 1; }
		80% { opacity: 1; }
		100% { opacity: 0; }
	}
	
	.rotate-animation {
		width: 160rpx;
		height: 160rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.phone-container {
		width: 100rpx;
		height: 100rpx;
		animation: rotatePhone 2s infinite ease-in-out;
		transform-style: preserve-3d;
	}
	
	@keyframes rotatePhone {
		0% {
			transform: rotateZ(0deg);
		}
		15% {
			transform: rotateZ(0deg);
		}
		30% {
			transform: rotateZ(-90deg);
		}
		70% {
			transform: rotateZ(-90deg);
		}
		85% {
			transform: rotateZ(0deg);
		}
		100% {
			transform: rotateZ(0deg);
		}
	}
	
	.phone {
		width: 80rpx;
		height: 140rpx;
		background-color: #333;
		border-radius: 16rpx;
		position: relative;
		transform: translateY(-20rpx);
		border: 2rpx solid #999;
		box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
	}
	
	.phone-inner {
		position: absolute;
		top: 10rpx;
		left: 10rpx;
		right: 10rpx;
		bottom: 10rpx;
		background-color: #00C1CC;
		border-radius: 6rpx;
	}
	
	.phone-button {
		position: absolute;
		bottom: 6rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: #222;
		border: 1rpx solid #999;
	}
	
	/* 添加横屏底部控制栏样式 */
	.bottom-controls {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 30rpx 50rpx;
		z-index: 10;
		transition: all 0.3s ease;
		height: 140rpx;
	}
	
	.bottom-gradient {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 200rpx;
		background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
		z-index: -1;
	}
	
	.bottom-buttons {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
	
	.bottom-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-left: 40rpx;
	}
	
	.bottom-btn text {
		font-size: 20rpx;
		color: #FFFFFF;
		margin-top: 8rpx;
	}
	
	.bottom-icon-wrapper {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.bottom-icon-wrapper.active {
		background-color: rgba(255, 95, 95, 0.2);
		color: #FF5F5F;
	}
	
	.bottom-controls.controls-hidden {
		opacity: 0;
		transform: translateY(100%);
	}
	
	/* 礼物弹窗样式 */
	.gift-container {
		background-color: #121212;
		border-radius: 30rpx 30rpx 0 0;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	.gift-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #333333;
	}
	
	.gift-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
	}
	
	.gift-close {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.gift-grid {
		flex: 1;
		max-height: 60vh;
		padding: 10rpx;
		overflow-y: auto;
	}
	
	.landscape-gift-grid {
		flex: 1;
		overflow-y: auto;
		padding: 10rpx;
	}
	
	.gift-grid-inner {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
	}
	
	.gift-grid-inner.landscape {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
	}
	
	.gift-item {
		width: 25%;
		padding: 16rpx;
		display: inline-block;
		box-sizing: border-box;
		text-align: center;
	}
	
	.landscape-gift-grid .gift-item {
		width: 33.33%;
		padding: 16rpx;
		display: inline-block;
		box-sizing: border-box;
		text-align: center;
	}
	
	.gift-selected {
		background-color: rgba(255, 95, 95, 0.2);
		transform: translateY(-5rpx);
	}
	
	.gift-image {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-bottom: 10rpx;
		display: inline-block;
	}
	
	.gift-name {
		font-size: 24rpx;
		color: #FFFFFF;
		margin-bottom: 4rpx;
		text-align: center;
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.gift-price {
		display: none;
	}
	
	.gift-button-row {
		padding: 20rpx 30rpx;
		display: flex;
		justify-content: center;
		background-color: rgba(0, 0, 0, 0.5);
		position: sticky;
		bottom: 0;
		width: 100%;
		z-index: 2;
	}
	
	.gift-send-btn {
		width: 80%;
		height: 80rpx;
		background-color: #333333;
		border-radius: 40rpx;
		color: #CCCCCC;
		font-size: 28rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.gift-send-btn.active {
		background-color: #FF5F5F;
		color: #FFFFFF;
	}
	
	/* 礼物动画样式 */
	.gift-animation-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 5;
		pointer-events: none;
	}
	
	.flying-gift {
		position: absolute;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.flying-gift.flyUp {
		animation: flyUp 3s ease-out forwards;
	}
	
	.flying-gift.flyUpLeft {
		animation: flyUpLeft 3s ease-out forwards;
	}
	
	.flying-gift.flyUpRight {
		animation: flyUpRight 3s ease-out forwards;
	}
	
	.flying-gift.bounce {
		animation: bounce 3s ease-out forwards;
	}
	
	.flying-gift.spin {
		animation: spin 3s ease-out forwards;
	}
	
	@keyframes flyUpLeft {
		0% {
			opacity: 0;
			transform: translate(0, 100rpx) scale(0.8);
		}
		20% {
			opacity: 1;
			transform: translate(0, 0) scale(1);
		}
		80% {
			opacity: 1;
			transform: translate(-100rpx, -200rpx) scale(1);
		}
		100% {
			opacity: 0;
			transform: translate(-200rpx, -400rpx) scale(0.8);
		}
	}
	
	@keyframes flyUpRight {
		0% {
			opacity: 0;
			transform: translate(0, 100rpx) scale(0.8);
		}
		20% {
			opacity: 1;
			transform: translate(0, 0) scale(1);
		}
		80% {
			opacity: 1;
			transform: translate(100rpx, -200rpx) scale(1);
		}
		100% {
			opacity: 0;
			transform: translate(200rpx, -400rpx) scale(0.8);
		}
	}
	
	@keyframes bounce {
		0%, 20%, 50%, 80%, 100% {
			transform: translateY(0);
		}
		40% {
			transform: translateY(-60rpx);
		}
		60% {
			transform: translateY(-30rpx);
		}
		90% {
			transform: translateY(-15rpx);
		}
	}
	
	@keyframes spin {
		0% {
			opacity: 0;
			transform: rotate(0deg) scale(0.8);
		}
		20% {
			opacity: 1;
			transform: rotate(0deg) scale(1);
		}
		80% {
			opacity: 1;
			transform: rotate(720deg) scale(1);
		}
		100% {
			opacity: 0;
			transform: rotate(1080deg) scale(0.8);
		}
	}
	
	.gift-sender {
		font-size: 24rpx;
		color: #FFFFFF;
		background-color: rgba(0, 0, 0, 0.5);
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		margin-bottom: 8rpx;
	}
	
	.gift-flying-image {
		width: 120rpx;
		height: 120rpx;
		animation: pulse 1s infinite alternate;
	}
	
	.gift-flying-name {
		font-size: 24rpx;
		color: #FFFFFF;
		background-color: rgba(0, 0, 0, 0.5);
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		margin-top: 8rpx;
	}
	
	@keyframes pulse {
		0% {
			transform: scale(1);
		}
		100% {
			transform: scale(1.1);
		}
	}
	
	@keyframes flyUp {
		0% {
			opacity: 0;
			transform: translateY(100rpx) scale(0.8);
		}
		20% {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
		80% {
			opacity: 1;
			transform: translateY(-200rpx) scale(1);
		}
		100% {
			opacity: 0;
			transform: translateY(-400rpx) scale(0.8);
		}
	}
	
	/* 横屏模式下的礼物侧边面板 */
	.landscape-gift-panel {
		position: fixed;
		top: 0;
		right: -400rpx;
		width: 400rpx;
		height: 100vh;
		z-index: 15;
		display: flex;
		justify-content: flex-end;
		transition: transform 0.3s ease-out;
	}
	
	.landscape-gift-panel.panel-open {
		transform: translateX(-400rpx);
	}
	
	.landscape-gift-content {
		width: 400rpx;
		height: 100vh;
		background-color: rgba(18, 18, 18, 0.8);
		border-radius: 20rpx 0 0 20rpx;
		display: flex;
		flex-direction: column;
		box-shadow: -10rpx 0 30rpx rgba(0, 0, 0, 0.5);
	}
	
	/* 视频缩小样式 - 移除，不再缩小视频 */
	.video-shrink {
		transition: all 0.3s ease;
	}
	
	/* 礼物按钮样式 */
	.gift-send-btn {
		width: 80%;
		height: 80rpx;
		background-color: #FF5F5F;
		border-radius: 40rpx;
		color: #FFFFFF;
		font-size: 28rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.gift-send-btn.active {
		background: linear-gradient(135deg, #FF5F5F, #FF3030);
		box-shadow: 0 4rpx 12rpx rgba(255, 95, 95, 0.4);
	}
	
	/* 添加点赞动画组件 - 根据横竖屏使用不同GIF */
	.like-animation-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		z-index: 8;
		display: flex;
		justify-content: center;
		align-items: center;
		pointer-events: none;
		animation: fadeInOut 2s ease-in-out;
	}
	
	.like-gif {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	@keyframes fadeInOut {
		0% { opacity: 0; }
		20% { opacity: 1; }
		80% { opacity: 1; }
		100% { opacity: 0; }
	}
	
	/* 修复礼物项样式 */
	.gift-item {
		width: 25%;
		padding: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		transition: all 0.2s ease;
		border-radius: 12rpx;
	}
	
	.landscape-gift-grid .gift-item {
		width: 33.33%;
		padding: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	/* 礼物网格样式 */
	.landscape-gift-grid {
		flex: 1;
		overflow-y: scroll;
		padding: 20rpx;
		display: flex;
		flex-wrap: wrap;
	}
	
	/* 礼物面板标题 */
	.gift-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #333333;
		background-color: rgba(0, 0, 0, 0.7);
	}
	
	/* 隐藏初始加载中的视频 */
	.hidden-video {
		opacity: 0;
	}
	
	/* 横屏弹幕式礼物动画样式 */
	.landscape-gift-danmaku-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 15;
		pointer-events: none;
		overflow: hidden;
	}
	
	.gift-danmaku {
		position: absolute;
		right: -300px;
		display: flex;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 30px;
		padding: 6px 12px;
		animation: danmakuMove 10s linear forwards;
		z-index: 15;
	}
	
	@keyframes danmakuMove {
		from {
			right: -300px;
		}
		to {
			right: 100%;
		}
	}
	
	.danmaku-avatar {
		width: 30px;
		height: 30px;
		border-radius: 50%;
		margin-right: 8px;
		border: 1px solid rgba(255, 255, 255, 0.3);
	}
	
	.danmaku-content {
		display: flex;
		align-items: center;
	}
	
	.danmaku-sender {
		color: #FF9500;
		font-size: 14px;
		margin-right: 4px;
		max-width: 80px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.danmaku-text {
		color: #FFFFFF;
		font-size: 14px;
		margin-right: 4px;
	}
	
	.danmaku-gift-icon {
		width: 24px;
		height: 24px;
		margin: 0 4px;
	}
	
	.danmaku-gift-name {
		color: #FF5F5F;
		font-size: 14px;
		font-weight: bold;
	}
	
	/* 确保video标签样式 */
	:deep(.transparent-video) {
		width: 100%;
		height: auto;
		object-fit: contain;
		z-index: 10080 !important;
	}
</style> 