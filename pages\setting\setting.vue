<template>
	<view>
		<view>
			<!-- <view class="dis-ali mt10 mb10" style="background-color: #fff;padding: 20upx;justify-content: space-between;"@click="goUrl(1)" v-if="!login">
				<view>
					<image src="../../static/1cc249a937fde3dc41171d7e9b610ea.png" mode="widthFix" style="width: 100upx;"></image>
				</view>
				<view class="dis-ali">
					<view style="color: #7c7b7b;font-size: 28upx;">去登录</view>
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view> -->
			<view class="dis-ali  " style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" @click="goUrl(2)">
				<view>
					个人信息
				</view>
				<view class="dis-ali">
					<view style="color: #7c7b7b;font-size: 28upx;"></view>
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view>
			<!-- <view class="dis-ali  mb10" style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;">
				<view>
					登录手机
				</view>
				<view class="dis-ali">
					<view style="color: #7c7b7b;font-size: 28upx;">{{detail.mobile}}</view>
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view> -->
			<view class="dis-ali  " style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" @click="goUrl(3)">
				<view>
					隐私设置
				</view>
				<view class="dis-ali">
					<!-- <view style="color: #7c7b7b;font-size: 28upx;">去登录</view> -->
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view>
			<view class="dis-ali "  @click="goUrl(4)"  style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" >
				<view>
					通知管理
				</view>
				<view class="dis-ali">
					<!-- <view style="color: #7c7b7b;font-size: 28upx;">去登录</view> -->
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view>
			<view class="dis-ali mb10" @click="clearCache" style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;">
				<view>
					清除缓存
				</view>
				<view class="dis-ali">
					<view style="color: #7c7b7b;font-size: 28upx;margin-right: 10upx;">{{cacheSize}}</view>
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view>
			
			
			<!-- <view class="dis-ali mt10 " style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);">
				<view>
					意见反馈
				</view>
				<view class="dis-ali">
					
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view> -->
			<!-- <view class="dis-ali  " style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);">
				<view>
					个人信息保护
				</view>
				<view class="dis-ali">
					
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view> -->
			<view class="dis-ali  " style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" @click="goUrl(6)">
				<view>
					注销账号
				</view>
				<view class="dis-ali">
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view>
			<view class="dis-ali  mb10" style="background-color: #fff;padding: 30upx 20upx;justify-content: space-between;" @click="goUrl(5)">
				<view>
					关于生意港
				</view>
				<view class="dis-ali">
					<view style="color: #7c7b7b;font-size: 28upx;">当前版本V{{system.appWgtVersion}}</view>
					<view style="margin-top: 4upx;">
						<u-icon name="arrow-right" size="14px" color=" #7c7b7b"></u-icon>
					</view>
				</view>
			</view>
			<view class="dis-ali  mb10" style="background-color: #fff;padding: 30upx 20upx;justify-content: center;font-size: 32upx;" @click="goout">
				<view>
					退出登录
				</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				login:'',
				detail:uni.getStorageSync('info'),
				cacheSize: '0B',
				system:{
					appWgtVersion:''
				}
			}
		},
		onLoad() {
			this.login=getApp().globalData.login
			this.getCacheSize()
			uni.getSystemInfo({
				success: (res) => {
					console.log(res)
					this.system=res
					uni.setStorageSync('system',res)
				}
			})
		},
		methods: {
			goUrl(type){
				if(type==1){
					uni.navigateTo({
						url:'/pages/sign/sign'
					})
				}
				if(type==2){
					uni.navigateTo({
						url:'/pages/personal/identity'
					})
				}
				if(type==3){
					uni.navigateTo({
						url:'/pages/yinsi/yinsi'
					})
				}
				if(type==4){
					uni.navigateTo({
						url:'/pages/yinsi/tongzhi'
					})
				}
				if(type==5){
						uni.navigateTo({
						url:'/pages/setting/aboutMe'
					})
				}
				if(type==6){
					uni.navigateTo({
						url:'/pages/yinsi/policy?type=99&delete=true'
					})
				}
			},
			goout(){
				this.login=false
				getApp().globalData.login=false
				uni.removeStorageSync('sygtoken')
				uni.removeStorageSync('defaultCompany')
				uni.removeStorageSync('syguser')
				console.log(this.login)
				if(this.login==false){
					uni.reLaunch({
						url:'/pages/sign/sign'
					})
				}
				
				
			},
			getCacheSize() {
				// #ifdef APP-PLUS
				try {
					plus.cache.calculate((size) => {
						let sizeStr = ''
						if (size < 1024) {
							sizeStr = size + 'B'
						} else if (size < 1024 * 1024) {
							sizeStr = (size / 1024).toFixed(2) + 'KB'
						} else if (size < 1024 * 1024 * 1024) {
							sizeStr = (size / (1024 * 1024)).toFixed(2) + 'MB'
						} else {
							sizeStr = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
						}
						this.cacheSize = sizeStr
					})
				} catch (e) {
					console.error('获取缓存大小失败:', e)
					this.cacheSize = '获取失败'
				}
				// #endif
				
				// #ifdef H5
				// H5环境下可以获取localStorage大小
				try {
					let size = 0
					for (let key in window.localStorage) {
						if (window.localStorage.hasOwnProperty(key)) {
							size += window.localStorage.getItem(key).length
						}
					}
					this.cacheSize = (size / 1024).toFixed(2) + 'KB'
				} catch (e) {
					console.error('获取缓存大小失败:', e)
					this.cacheSize = '获取失败'
				}
				// #endif
			},
			clearCache() {
				uni.showModal({
					title: '提示',
					content: '确定要清除缓存吗？',
					success: (res) => {
						if (res.confirm) {
							// #ifdef APP-PLUS
							plus.cache.clear(() => {
								uni.showToast({
									title: '清除成功',
									icon: 'success'
								})
								this.getCacheSize()
							})
							// #endif
							
							// #ifdef H5
							try {
								window.localStorage.clear()
								uni.showToast({
									title: '清除成功',
									icon: 'success'
								})
								this.getCacheSize()
							} catch (e) {
								uni.showToast({
									title: '清除失败',
									icon: 'none'
								})
							}
							// #endif
							
							// 保留必要的数据
							const token = uni.getStorageSync('token')
							const userInfo = uni.getStorageSync('info')
							uni.clearStorageSync()
							if (token) uni.setStorageSync('token', token)
							if (userInfo) uni.setStorageSync('info', userInfo)
						}
					}
				})
			},
		}
	}
</script>

<style>

</style>
